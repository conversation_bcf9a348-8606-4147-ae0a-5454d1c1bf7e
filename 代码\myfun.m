% 构造环流量法计算程序的目标函数
% x为输入的两个两个换热站的节点编号
% y为计算得到的换热站到节点之间管段压降
% 测试为 x = [1 4];    y = myfun(x)
function y = myfun(x,LL1,LL2)

data1=xlsread('许仕荣87页管网.xlsx','管网基本参数');
data2=xlsread('许仕荣87页管网.xlsx','管段数据');
data3=xlsread('许仕荣87页管网.xlsx','节点流量');
data4=xlsread('许仕荣87页管网.xlsx','水源数据');

%****************************************************
% 此处为与源程序修改的内容
data2(1,3) = x(1);
data2(5,3) = x(2);
data2(1,6) = LL1(1);%考虑管长
data2(5,6) = LL2(1);%考虑管长
%****************************************************

N=data1(1);%管段数
MT=data1(2);%计入水源后总节点数
L=data1(3);%实环数
LX=data1(4);%虚实环数
N_tower=data1(5);%水塔数
N_pump=data1(6);%泵站数
N_source=N_tower+N_pump;%水源数（水塔数+泵站数）
M=MT-N_source;%不含水源的节点数
C=data1(7);%水头参数
NM(:,1:2)=data2(:,2:3);%管段与上下游节点关联矩阵 NM N×2
%NL,管段与环的关联矩阵（N行，2列矩阵，正向环对应NL（i,1）,逆向环对应NL（i,2），只属于1个环，则另一元素赋0）
NL(:,1:2)=data2(:,4:5);%管段所属环关联矩阵 NL N×2
%  %LD,D,管长 管径,m
LD=data2(:,6);
D=data2(:,7);
m=data2(1,8);%摩阻指数
Q=data2(:,9);%管段初始流量m^3/s
if LX~=0%如果虚实环数不为0，从data3和data4中读取以下数据并赋值：
    QX=data3(:,2);%QX,节点流量m^3/s
    HX=data4(:,2); %水源总水头，
    SX=data4(:,3); %水源出水管虚阻耗系数(s^2/(m^3))，
    NQ=data4(:,4); %水源出水管管段编号
    %NX（i,1）,NX(i,2)，虚环泵站始端水源编号和末端水源编号，泵站出水管方向与环方向一致为始端水源）
    NX(:,1)=data4(:,5);
    NX(:,2)=data4(:,6);
end
EZ=0.05;%回路水头闭合容差
STR=0.5;%松弛因子
%*******************************************
%节点初始压头
E=ones(M,1)*70;%非水源节点的初始压头设置为70
for I=1:MT-M
    E(I+M)=HX(I);%水源节点的压头设置为 HX 中的值
end
%生成MIS,MJS矩阵,矩阵MIS与MJS相配合，元素MIS（I,J）确定节点I与哪几个管段相连，且标识节点I是管段J的上游节点还是下游节点。
%若是下游节点，则对应的管段号为负。
%矩阵MJS（I,J）标识节点I关联的管段的另一个节点。
for I=1:M %对节点循环
    K=0;%变量
    for J=1:N %对管段循环
        if NM(J,1)==I %节点I属于管段J，且I是管段J的上游节点
            K=K+1;
            MIS(I,K)=J;%节点I关联的第K个管段是J
            MJS(I,K)=NM(J,2);%表示节点I关联的第K个管段的另一个节点是NM(J,2)
        end
        if NM(J,2)==I %节点I属于管段J，且I是管段J的下游节点
            K=K+1;
            MIS(I,K)=-J;
            MJS(I,K)=NM(J,1);
        end
    end %end J,对管段循环
    MM(I)=K;%表示节点I关联的管段数
end %end I,对节点循环
% NM
% MIS
% MJS
% MM
%计算管段流量系数R
for I=1:N
    R(I)=0.27853*C*D(I)^2.63/LD(I)^0.54;
end
% 出水管段的流量系数
if N_pump~=0
    for I=1:N_pump
        R(NQ(I))=1/SX(I)^0.5;
    end
end
R=R';
% R
KKN=[];%用于存储每次迭代的编号。
EQM=[];%用于存储每次迭代中的节点流量差的最大值
%**************************************************
for KK=1:10
    % disp('********  KK  *************')
    KK;
    for I=1:N %对管段循环
        ET=E(NM(I,1))-E(NM(I,2));%管段I的两端节点的压头差ET
        Q(I)=R(I)*abs(ET)^0.54*sign(ET);%计算管段I的流量Q(I)
    end
    if N_pump~=0
        for I=1:N_pump
            ET=E(NM(NQ(I),1))-E(NM(NQ(I),2));%对每个管段计算压力差 ET 并更新流量 Q
            Q(NQ(I))=R(NQ(I))*abs(ET)^0.5;   %如果有泵站，对泵站的出水管段进行特殊处理
        end
    end
    % Q
    %EQ--->f, J*DE=f；计算节点流量方程 EQ
    for I=1:M
        EQ(I)=QX(I);%初始化节点I的流量平衡EQ(I)为节点I的给定流量QX(I)
        for J=1:MM(I)%对节点 I 关联的每个管段进行迭代
            T=MIS(I,J);%取出节点I关联的第J个管段的编号T
            EQ(I)=EQ(I)+Q(abs(T))*sign(T);
        end
    end
    %生成系数矩阵AJ,用于线性方程组AJ*DE=bb的求解。这个方程组在迭代过程中用于更新节点的压头E;
    for I=1:N %对管段循环
        ET=E(NM(I,1))-E(NM(I,2));
        if(ET==0)
            ET=0.0001;%避免在后续计算中出现除以0的错误。
        end
        QJ(I)=R(I)/abs(ET)^0.46*0.54;
    end%根据当前节点的压头差计算每个管段的流量导数。
    if N_pump~=0
        for I=1:N_pump
            ET=E(NM(NQ(I),1))-E(NM(NQ(I),2));
            if(ET==0)
                ET=0.00001;
            end
            QJ(NQ(I))=R(NQ(I))/abs(ET)^0.5*0.5;
        end
    end
    AJ=zeros(M,M);%初始化 AJ 矩阵为 M x M 的零矩阵
    for I=1:M %对节点循环
        for J=1:MM(I)
            NT=abs(MIS(I,J));%按顺序取出节点I对应的管段号，节点I是管段J的一个节点，若管段号为负，则节点I是管段I的下游节点。
            NT1=MJS(I,J);%按顺序取节点I对应的管段J的另一节点。
            AJ(I,I)=AJ(I,I)+QJ(NT);%更新AJ矩阵的对角线元素，累加节点I的流量导数QJ(NT)。
            if NT1<=M%如果NT1是一个普通节点（不包括水源节点），更新AJ矩阵的非对角线元素
                AJ(I,NT1)=AJ(I,NT1)-QJ(NT);
            end
        end
        if AJ(I,I)==0
            AJ(I,I)=1e11;% 防止对角线元素为 0
        end
    end
    % AJ
    EQ_max=max(EQ);%计算当前迭代中节点流量平衡的最大偏差，用于后续判断迭代收敛性。
    bb=EQ';%将流量平衡数组EQ转置，准备用于线性方程组的求解
    DE=AJ\bb;%求解线性方程组，得到每个节点的压头修正值DE。
    % DE
    for I=1:M
        E(I)=E(I)-STR*DE(I);%根据压头修正值 DE 更新每个节点的压头E
    end
    KKN=[KKN,KK];%记录当前的迭代次数
    EQM=[EQM,EQ_max];%记录当前的最大流量平衡偏差。
    % plot(KKN,EQM,'-*');%绘制迭代次数与最大流量平衡偏差的关系图
end


%****************************************************
% 计算压降差
y = (E(15)-E(x(1))) + (E(16)-E(x(2)));
% Q 记录管段流量
%****************************************************
