clc
clear
close all

% 读取 Excel 文件中的数据
data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
data2 = xlsread('许仕荣87页管网.xlsx', '管段数据');
data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');

N = 11;  % 可选择的节点总数

% 考虑管长
LLL = [1000, 3000; 1300, 2000; 1500, 800; 2000, 240; 700, 3500; 1400, 3000; 1900, 2000; 2500, 1000; 2000, 4000; 3500, 3500; 4000, 1800];
LL1 = zeros(50, 1);
LL2 = zeros(50, 1);

%% 粒子群算法相关参数
np = 100;  % 种群个数
gen = 100;  % 迭代次数
dim = 2;  % 决策变量的维度
c1 = 1;  % 学习因子
c2 = 1;
w_max = 0.9;  % 惯性权重
w_min = 0.1;
v_max = 0.3 * N;  % 粒子的速度限制
v_min = -v_max;

%% 初始化粒子集合和速度
for i = 1:np
    x(i,:) = randperm(N, dim);  % 使用randperm确保粒子位置不重复
    LL1(i, 1) = LLL(x(i, 1), 1);  % 考虑管长
    LL2(i, 1) = LLL(x(i, 2), 2);  % 考虑管长
end

v = (rand(np, dim) - 0.5) * 2 * v_max;  % 随机初始化速度，并添加扰动

%% 计算初始化 个体最优 和 全局最优
pbest = x;
for i = 1:np
    pbest_fit(i, 1) = myfun1b(x(i, :), LL1(i, 1), LL2(i, 1), data1, data2, data3, data4);  % 存储个体最优值
end

[global_best_fit, idx] = min(pbest_fit);
global_best = pbest(idx(1), :);

%% 模拟退火算法参数
initial_temp = 1000;  % 初始温度
final_temp = 1;  % 最终温度
alpha = 0.98;  % 温度衰减系数
temp = initial_temp;

%% 迭代计算
for t = 1:gen
    w = (w_max - w_min) * (1 - t / gen) + w_min;  % 动态权重系数

    for i = 1:np
        % 更新粒子的速度
        v(i,:) = w * v(i,:) + c1 * rand * (pbest(i,:) - x(i,:)) + c2 * rand * (global_best - x(i,:));
        
        % 对速度超出边界进行限制
        v(i,:) = max(min(v(i,:), v_max), v_min);
        
        % 更新粒子的位置，并确保为整数
        x(i,:) = x(i,:) + round(v(i,:));
        
        % 检查位置边界
        x(i,:) = max(min(x(i,:), N), 1);
        
        % 防止重复解：检查是否有重复的位置
        while length(x(i,:)) ~= length(unique(x(i,:)))
            x(i,:) = randperm(N, dim);  % 确保生成不重复的解
        end
        
        % 模拟退火算法：对粒子位置进行局部调整
        new_position = simulated_annealing(x(i,:), temp, data1, data2, data3, data4, LL1(i, 1), LL2(i, 1));
        
        % 如果新解更好或根据模拟退火准则接受新解
        if myfun1b(new_position, LL1(i, 1), LL2(i, 1), data1, data2, data3, data4) < myfun1b(x(i,:), LL1(i, 1), LL2(i, 1), data1, data2, data3, data4)
            x(i,:) = new_position;
        end
        
        % 再次检查解是否重复
        while length(x(i,:)) ~= length(unique(x(i,:)))
            x(i,:) = randperm(N, dim);  % 确保生成不重复的解
        end
    end

    % 计算个体最优和全局最优
    for i = 1:np
        fitness = myfun1b(x(i,:), LL1(i, 1), LL2(i, 1), data1, data2, data3, data4);  % 考虑管长
        if fitness < pbest_fit(i)
            pbest_fit(i) = fitness;
            pbest(i,:) = x(i,:);
        end
        if fitness < global_best_fit
            global_best_fit = fitness;
            global_best = x(i,:);
        end
    end

    Convergence_curve(t) = global_best_fit;  % 记录每次迭代中的最优值
    disp(['第', num2str(t), '代计算完成。最优解为', num2str(global_best), '。适应度值为', num2str(global_best_fit)]);

    % 温度衰减
    temp = temp * alpha;
end

% 绘制收敛曲线
figure;
plot(Convergence_curve);

%% 模拟退火算法函数
function new_position = simulated_annealing(position, temp, data1, data2, data3, data4, LL1, LL2)
    % 生成新解
    new_position = position;
    rand_idx = randi([1, length(position)], 1);  % 随机选择一个元素
    new_position(rand_idx) = randi([1, length(data1)], 1);  % 随机更新该位置

    % 计算新解的适应度
    new_fitness = myfun1b(new_position, LL1, LL2, data1, data2, data3, data4);
    
    % 如果新解更好，接受新解
    if new_fitness < myfun1b(position, LL1, LL2, data1, data2, data3, data4)
        return;
    else
        % 如果新解较差，根据温度和概率接受新解
        delta_fitness = new_fitness - myfun1b(position, LL1, LL2, data1, data2, data3, data4);
        if rand < exp(-delta_fitness / temp)
            return;
        end
    end
end

