
%纯粹的离散优化（选择节点）转变为一个混合整数/连续优化问题，
% 接入点选择是离散的，而管道长度是连续的（或至少可以在某个范围内变化）。
%%% 主优化脚本：结合PSO和GA优化泵站接入点及连接管长
clc;          % 清除命令行窗口
clear;        % 清除工作空间中的所有变量
close all;    % 关闭所有打开的图形窗口

% --- 1. 数据加载与初始化 ---
disp('开始加载数据...');
try
    data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
    data2_orig = xlsread('许仕荣87页管网.xlsx', '管段数据'); % 加载原始数据，不直接修改
    data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
    data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
    disp('数据加载成功。');
catch ME
    disp('错误：无法从Excel文件加载数据。请检查文件名和工作表名是否正确，以及文件是否存在。');
    disp(ME.message);
    return;
end

% --- 定义允许的节点和对应的长度范围 ---
allowed_nodes_pump1 = [1, 5, 6, 9, 10];
length_ranges_pump1 = [ % [min_len, max_len] for each node in allowed_nodes_pump1
    1000, 1000;  % Node 1
    380, 800;  % Node 5
    800,  1500;  % Node 6
    1350, 2000;  % Node 9
    1500, 3000   % Node 10
];

allowed_nodes_pump2 = [ 2, 3, 4, 7];
length_ranges_pump2 = [ % [min_len, max_len] for each node in allowed_nodes_pump2
    1750, 2500;   % Node 2
    400, 800;   % Node 3
    240, 240;   % Node 4
    1200, 2000    % Node 7
];

N1_allowed = length(allowed_nodes_pump1);
N2_allowed = length(allowed_nodes_pump2);

% 检查范围定义是否匹配
if size(length_ranges_pump1, 1) ~= N1_allowed
    error('泵1的长度范围定义数量与允许节点数不匹配');
end
if size(length_ranges_pump2, 1) ~= N2_allowed
    error('泵2的长度范围定义数量与允许节点数不匹配');
end 

% 定义参考数据
% 参考流量 Q_original
Q_original = [
    0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
    0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
    0.4213; 0.3208
];
if size(Q_original,1) ~= data1(1) % data1(1) 是管段数 N
    warning('提供的参考流量 Q_original 长度 (%d) 与data1中管段数 (%d) 不符，请检查。', size(Q_original,1), data1(1));
end

% 定义参考管段水头损失 H_loss_original
H_loss_original = [
    12.2700; 2.0000; -0.3900; -2.0900; 1.8000; 1.4700; 1.7600; 4.9600;
    3.0900; 2.1200; 3.6100; -5.4000; 2.9600; 2.4600; 2.3700; 1.4400;
    1.7900; 3.5200; -8.6600; 0.5100; 5.1600; 12.0500
];
if size(H_loss_original,1) ~= data1(1) % data1(1) 是管段数 N
    error('提供的参考水头损失 H_loss_original 长度 (%d) 与管网管段数 (%d) 不符。', size(H_loss_original,1), data1(1));
end
disp('参考流量和参考水头损失数据已定义。');

% E1 (参考节点压头) 仍然在此定义，但不再直接作为约束传递给 myfun1b_mod
% 它可能用于其他分析或比较，如果不需要可以移除。
E1 = [
    74.576428; 72.573442; 72.167468; 75.159936; 73.109308; 70.810565; 67.205452;
    72.065420; 70.142379; 68.351104; 64.834299; 73.496291; 86.845284; 76.950363;
    92.000000; 89.000000; 74.000000
];
if size(E1,1) ~= data1(2) % data1(2) 是总节点数 MT
    warning('提供的E1长度 (%d) 与data1中总节点数 (%d) 不符，请检查。', size(E1,1), data1(2));
end


% --- 2. PSO算法相关参数定义 ---
disp('设置PSO和GA参数...');
np = 200;         % 种群数量
gen = 100;       % 最大迭代次数
dim = 4;         % 决策变量维度: [idx1, len1, idx2, len2]
c1 = 2;
c2 = 2;
w_max = 0.9;
w_min = 0.1;

% 速度限制
v_max_idx = 1;    % 节点索引最大变化量 (取整后)
v_min_idx = -1;   % 节点索引最小变化量
v_max_len = 10;   % 长度最大变化量 (例如: 10米/迭代) - 需要根据范围调整
v_min_len = -10;  % 长度最小变化量
Vmax = [v_max_idx, v_max_len, v_max_idx, v_max_len]; % 按维度设置
Vmin = [v_min_idx, v_min_len, v_min_idx, v_min_len];

mutation_prob_ga = 0.3; % GA变异概率
crossover_prob_ga = 0.7; % GA交叉概率

% --- 3. 初始化粒子群 (位置和速度) ---
disp('初始化粒子群...');
x = zeros(np, dim); % 位置: [idx1, len1, idx2, len2]
v = zeros(np, dim); % 速度

for i = 1:np
    % 初始化节点索引
    idx1 = randi(N1_allowed);
    idx2 = randi(N2_allowed);
    x(i, 1) = idx1;
    x(i, 3) = idx2;

    % 根据选定的节点索引，在其范围内初始化长度
    minL1 = length_ranges_pump1(idx1, 1); maxL1 = length_ranges_pump1(idx1, 2);
    minL2 = length_ranges_pump2(idx2, 1); maxL2 = length_ranges_pump2(idx2, 2);
    x(i, 2) = rand() * (maxL1 - minL1) + minL1;
    x(i, 4) = rand() * (maxL2 - minL2) + minL2;

    % 初始化速度
    v(i, 1) = rand() * (v_max_idx - v_min_idx) + v_min_idx;
    v(i, 2) = rand() * (v_max_len - v_min_len) + v_min_len;
    v(i, 3) = rand() * (v_max_idx - v_min_idx) + v_min_idx;
    v(i, 4) = rand() * (v_max_len - v_min_len) + v_min_len;
end
disp('粒子群初始化完成。');

% --- 4. 计算初始适应度，并初始化个体最优和全局最优 ---
disp('计算初始适应度...');
pbest = x;
pbest_fit = zeros(np, 1);
global_best_fit = inf;
global_best = zeros(1, dim);

for i = 1:np
    % 调用修改后的目标函数, 传递 H_loss_original 而不是 E1
    pbest_fit(i) = myfun1b_mod(x(i,:), data1, data2_orig, data3, data4, Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);
    if pbest_fit(i) < global_best_fit
        global_best_fit = pbest_fit(i);
        global_best = x(i,:);
    end
end
disp(['初始全局最优适应度: ', num2str(global_best_fit)]);
% 显示初始最优解（将索引转换回节点号）
init_best_node1 = allowed_nodes_pump1(round(global_best(1)));
init_best_len1 = global_best(2);
init_best_node2 = allowed_nodes_pump2(round(global_best(3)));
init_best_len2 = global_best(4);
disp(['初始全局最优选点: 泵1->Node ', num2str(init_best_node1), ' (L=', sprintf('%.2f', init_best_len1), ...
      '), 泵2->Node ', num2str(init_best_node2), ' (L=', sprintf('%.2f', init_best_len2), ')']);


% --- 5. PSO-GA 主循环 ---
disp('开始PSO-GA迭代优化...');
Convergence_curve = zeros(gen, 1);

for t = 1:gen
    w = w_max - (w_max - w_min) * (t / gen);

    % -- PSO阶段 --
    for i = 1:np
        % 更新速度
        v(i,:) = w * v(i,:) + ...
                 c1 * rand(1, dim) .* (pbest(i,:) - x(i,:)) + ...
                 c2 * rand(1, dim) .* (global_best - x(i,:));

        % 限制速度 (按维度)
        v(i,:) = max(min(v(i,:), Vmax), Vmin);

        % 更新位置 x(i,:)
        x_new = x(i,:) + v(i,:); % 暂存新位置

        % 处理离散维度 (节点索引) 并进行边界处理 (周期性)
        new_idx1 = mod(round(x_new(1)) - 1, N1_allowed) + 1;
        new_idx2 = mod(round(x_new(3)) - 1, N2_allowed) + 1;

        % 处理连续维度 (长度) 并根据 *新* 的节点索引进行边界处理 (钳位)
        minL1 = length_ranges_pump1(new_idx1, 1); maxL1 = length_ranges_pump1(new_idx1, 2);
        new_len1 = max(min(x_new(2), maxL1), minL1); % 钳位到新索引对应的范围

        minL2 = length_ranges_pump2(new_idx2, 1); maxL2 = length_ranges_pump2(new_idx2, 2);
        new_len2 = max(min(x_new(4), maxL2), minL2); % 钳位到新索引对应的范围

        % 更新粒子位置
        x(i,:) = [new_idx1, new_len1, new_idx2, new_len2];
    end

    % -- 适应度评估与最优更新阶段 --
    for i = 1:np
        % 调用修改后的目标函数, 传递 H_loss_original 而不是 E1
        current_fitness = myfun1b_mod(x(i,:), data1, data2_orig, data3, data4, Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);

        % 更新个体最优
        if current_fitness < pbest_fit(i)
            pbest_fit(i) = current_fitness;
            pbest(i,:) = x(i,:);
        end

        % 更新全局最优
        if current_fitness < global_best_fit
            global_best_fit = current_fitness;
            global_best = x(i,:);
        end
    end

    % -- GA阶段 (基于pbest进行种群再生) --
    parents_for_ga = pbest;
    offspring_ga = zeros(np, dim);

    for k = 1:np
        % 选择两个不同的父代 (简化轮盘赌或锦标赛可选，这里用随机选择)
        p1_idx = randi(np);
        p2_idx = randi(np);
        while p2_idx == p1_idx
            p2_idx = randi(np);
        end
        parent1 = parents_for_ga(p1_idx, :);
        parent2 = parents_for_ga(p2_idx, :);
        child = parent1; % 默认继承parent1

        % 交叉 (模拟单点交叉，但作用于变量对)
        if rand < crossover_prob_ga
           if rand < 0.5 % 交叉泵1的节点和长度
               child(1:2) = parent2(1:2);
           end
           if rand < 0.5 % 交叉泵2的节点和长度
               child(3:4) = parent2(3:4);
           end
           % 交叉后需要重新检查长度边界 (因为节点可能变了)
           child_idx1 = round(child(1)); child_idx2 = round(child(3));
           child_idx1 = max(1, min(child_idx1, N1_allowed)); % 确保索引有效
           child_idx2 = max(1, min(child_idx2, N2_allowed));
           child(1) = child_idx1; child(3) = child_idx2; % 更新索引

           minL1 = length_ranges_pump1(child_idx1, 1); maxL1 = length_ranges_pump1(child_idx1, 2);
           child(2) = max(min(child(2), maxL1), minL1); % 钳位长度1

           minL2 = length_ranges_pump2(child_idx2, 1); maxL2 = length_ranges_pump2(child_idx2, 2);
           child(4) = max(min(child(4), maxL2), minL2); % 钳位长度2
        end

        % 变异
        if rand < mutation_prob_ga
            dim_to_mutate = randi(dim); % 选择变异维度 1, 2, 3, or 4
            current_child = child; % 操作副本

            if dim_to_mutate == 1 % 变异泵1节点索引
                current_child(1) = randi(N1_allowed);
                % 节点变异后，长度在 *新* 节点范围内重新随机生成
                new_idx1 = round(current_child(1));
                minL1 = length_ranges_pump1(new_idx1, 1); maxL1 = length_ranges_pump1(new_idx1, 2);
                current_child(2) = rand() * (maxL1 - minL1) + minL1;
            elseif dim_to_mutate == 2 % 变异泵1长度
                idx1 = round(current_child(1));
                minL1 = length_ranges_pump1(idx1, 1); maxL1 = length_ranges_pump1(idx1, 2);
                % 在当前节点允许范围内重新随机或加扰动
                current_child(2) = rand() * (maxL1 - minL1) + minL1; % 重新随机
                % 或者加扰动: current_child(2) = current_child(2) + (rand()-0.5) * (maxL1-minL1)*0.1;
                current_child(2) = max(min(current_child(2), maxL1), minL1); % 确保不出界
            elseif dim_to_mutate == 3 % 变异泵2节点索引
                current_child(3) = randi(N2_allowed);
                new_idx2 = round(current_child(3));
                minL2 = length_ranges_pump2(new_idx2, 1); maxL2 = length_ranges_pump2(new_idx2, 2);
                current_child(4) = rand() * (maxL2 - minL2) + minL2;
            else % 变异泵2长度
                idx2 = round(current_child(3));
                minL2 = length_ranges_pump2(idx2, 1); maxL2 = length_ranges_pump2(idx2, 2);
                current_child(4) = rand() * (maxL2 - minL2) + minL2; % 重新随机
                current_child(4) = max(min(current_child(4), maxL2), minL2);
            end
             child = current_child; % 应用变异结果
        end
        offspring_ga(k,:) = child;
    end

    % 用GA产生的子代替换当前种群x
    x = offspring_ga;

    % 记录当前迭代的全局最优适应度
    Convergence_curve(t) = global_best_fit;

    % 显示当前迭代信息
    current_best_node1 = allowed_nodes_pump1(round(global_best(1)));
    current_best_len1 = global_best(2);
    current_best_node2 = allowed_nodes_pump2(round(global_best(3)));
    current_best_len2 = global_best(4);
    disp(['第 ', num2str(t), ' 代完成。全局最优适应度: ', num2str(global_best_fit), ...
          ', 最优解: P1->N', num2str(current_best_node1), '(L=', sprintf('%.1f', current_best_len1), ...
          '), P2->N', num2str(current_best_node2), '(L=', sprintf('%.1f', current_best_len2), ')']);

end
disp('优化完成。');

% --- 6. 结果展示 ---
disp('-----------------------------------------');
disp('最终优化结果:');
disp(['全局最优适应度值 (目标函数值): ', num2str(global_best_fit)]);
final_best_node1 = allowed_nodes_pump1(round(global_best(1)));
final_best_len1 = global_best(2);
final_best_node2 = allowed_nodes_pump2(round(global_best(3)));
final_best_len2 = global_best(4);
disp(['最优泵站接入点与管长:']);
disp(['  泵站1 -> 节点 ', num2str(final_best_node1), ', 连接管长: ', sprintf('%.2f', final_best_len1), ' m']);
disp(['  泵站2 -> 节点 ', num2str(final_best_node2), ', 连接管长: ', sprintf('%.2f', final_best_len2), ' m']);

figure;
plot(1:gen, Convergence_curve, '-b', 'LineWidth', 1.5);
title('PSO-GA 优化收敛曲线 (节点+长度)', 'FontSize', 14);
xlabel('迭代次数', 'FontSize', 12);
ylabel('全局最优适应度值', 'FontSize', 12);
grid on;
legend('全局最优适应度');
disp('收敛曲线已绘制。');
