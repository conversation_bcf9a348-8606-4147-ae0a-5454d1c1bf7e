@echo off
echo Starting Python optimization system...
cd /d "%~dp0"
echo Current directory: %CD%

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo Neither python nor python3 found in PATH.
        echo Please install Python and add it to PATH.
        pause
        exit /b 1
    ) else (
        echo Running with python3...
        python3 python_optimization.py
    )
) else (
    echo Running with python...
    python python_optimization.py
)

echo.
echo Optimization completed. Press any key to exit...
pause
