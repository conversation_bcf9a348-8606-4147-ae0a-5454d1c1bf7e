%% 管网泵站接入点优化主运行脚本
% 该脚本整合了优化算法运行、结果分析和可视化
% 作者：AI Assistant
% 日期：2024

function run_optimization()
    clc; clear; close all;
    
    %% 1. 环境设置
    disp('=== 管网泵站接入点优化系统 ===');
    disp('正在初始化...');
    
    % 设置随机种子以确保结果可重现
    rng(42);
    
    % 记录开始时间
    start_time = tic;
    
    %% 2. 数据验证
    disp('1. 验证数据文件...');
    if ~exist('许仕荣87页管网.xlsx', 'file')
        error('找不到数据文件：许仕荣87页管网.xlsx');
    end
    disp('   数据文件验证通过');
    
    %% 3. 运行优化算法
    disp('2. 运行PSO-GA混合优化算法...');
    disp('   这可能需要几分钟时间，请耐心等待...');
    
    try
        % 运行主优化程序
        main1bpsogad;
        
        optimization_time = toc(start_time);
        disp(['   优化完成！总用时: ', sprintf('%.2f', optimization_time), ' 秒']);
        
    catch ME
        error('优化过程中出现错误: %s', ME.message);
    end
    
    %% 4. 结果分析
    disp('3. 分析优化结果...');
    analyze_optimization_results();
    
    %% 5. 生成可视化
    disp('4. 生成可视化图表...');
    generate_visualization();
    
    %% 6. 生成综合报告
    disp('5. 生成综合分析报告...');
    generate_comprehensive_report();
    
    disp('=== 优化分析完成 ===');
    disp('请查看生成的报告文件和图表');
end

%% 分析优化结果
function analyze_optimization_results()
    % 检查是否有优化结果
    if ~exist('global_best', 'var') || ~exist('global_best_fit', 'var')
        warning('未找到优化结果变量');
        return;
    end
    
    % 基本统计
    allowed_nodes_pump1 = [1, 5, 6, 9, 10];
    allowed_nodes_pump2 = [2, 3, 4, 7];
    
    final_node1 = allowed_nodes_pump1(round(global_best(1)));
    final_node2 = allowed_nodes_pump2(round(global_best(3)));
    
    fprintf('\n=== 结果分析 ===\n');
    fprintf('最优解详情:\n');
    fprintf('  泵站1: 节点%d, 管长%.2fm\n', final_node1, global_best(2));
    fprintf('  泵站2: 节点%d, 管长%.2fm\n', final_node2, global_best(4));
    fprintf('  目标函数值: %.6f\n', global_best_fit);
    
    % 约束验证
    if exist('fitness_history', 'var') && ~isempty(fitness_history)
        fprintf('\n收敛性分析:\n');
        fprintf('  初始适应度: %.6f\n', fitness_history(1));
        fprintf('  最终适应度: %.6f\n', fitness_history(end));
        improvement = fitness_history(1) - fitness_history(end);
        improvement_pct = improvement / fitness_history(1) * 100;
        fprintf('  改善幅度: %.6f (%.2f%%)\n', improvement, improvement_pct);
        fprintf('  实际迭代次数: %d\n', length(fitness_history));
    end
end

%% 生成可视化
function generate_visualization()
    try
        % 创建综合可视化图表
        figure('Name', '优化结果综合分析', 'Position', [100, 100, 1200, 800]);
        
        % 子图1：收敛曲线
        subplot(2, 2, 1);
        if exist('fitness_history', 'var') && ~isempty(fitness_history)
            plot(1:length(fitness_history), fitness_history, 'b-', 'LineWidth', 2);
            title('适应度收敛曲线');
            xlabel('迭代次数');
            ylabel('适应度值');
            grid on;
        else
            text(0.5, 0.5, '无收敛数据', 'HorizontalAlignment', 'center');
        end
        
        % 子图2：节点选择历史
        subplot(2, 2, 2);
        if exist('best_solutions_history', 'var') && ~isempty(best_solutions_history)
            allowed_nodes_pump1 = [1, 5, 6, 9, 10];
            allowed_nodes_pump2 = [2, 3, 4, 7];
            
            node1_history = allowed_nodes_pump1(round(best_solutions_history(:, 1)));
            node2_history = allowed_nodes_pump2(round(best_solutions_history(:, 3)));
            
            plot(1:length(node1_history), node1_history, 'r.-', 'MarkerSize', 8, 'DisplayName', '泵站1');
            hold on;
            plot(1:length(node2_history), node2_history, 'g.-', 'MarkerSize', 8, 'DisplayName', '泵站2');
            title('接入节点选择历史');
            xlabel('迭代次数');
            ylabel('节点编号');
            legend;
            grid on;
        else
            text(0.5, 0.5, '无历史数据', 'HorizontalAlignment', 'center');
        end
        
        % 子图3：管道长度历史
        subplot(2, 2, 3);
        if exist('best_solutions_history', 'var') && ~isempty(best_solutions_history)
            plot(1:size(best_solutions_history, 1), best_solutions_history(:, 2), 'm-', 'LineWidth', 1.5, 'DisplayName', '泵站1管长');
            hold on;
            plot(1:size(best_solutions_history, 1), best_solutions_history(:, 4), 'c-', 'LineWidth', 1.5, 'DisplayName', '泵站2管长');
            title('管道长度优化历史');
            xlabel('迭代次数');
            ylabel('长度 (m)');
            legend;
            grid on;
        else
            text(0.5, 0.5, '无历史数据', 'HorizontalAlignment', 'center');
        end
        
        % 子图4：最优解可视化
        subplot(2, 2, 4);
        if exist('global_best', 'var')
            allowed_nodes_pump1 = [1, 5, 6, 9, 10];
            allowed_nodes_pump2 = [2, 3, 4, 7];
            
            final_node1 = allowed_nodes_pump1(round(global_best(1)));
            final_node2 = allowed_nodes_pump2(round(global_best(3)));
            
            bar([1, 2], [final_node1, final_node2], 'FaceColor', [0.2, 0.6, 0.8]);
            title('最优接入节点');
            xlabel('泵站');
            ylabel('节点编号');
            set(gca, 'XTick', [1, 2], 'XTickLabel', {'泵站1', '泵站2'});
            grid on;
            
            % 添加数值标签
            text(1, final_node1 + 0.1, sprintf('节点%d', final_node1), 'HorizontalAlignment', 'center');
            text(2, final_node2 + 0.1, sprintf('节点%d', final_node2), 'HorizontalAlignment', 'center');
        else
            text(0.5, 0.5, '无最优解数据', 'HorizontalAlignment', 'center');
        end
        
        % 保存图表
        saveas(gcf, 'optimization_analysis.png');
        disp('   可视化图表已保存为 optimization_analysis.png');
        
    catch ME
        warning('生成可视化时出错: %s', ME.message);
    end
end

%% 生成综合报告
function generate_comprehensive_report()
    try
        fid = fopen('comprehensive_optimization_report.txt', 'w');
        if fid == -1
            warning('无法创建综合报告文件');
            return;
        end
        
        fprintf(fid, '管网泵站接入点优化综合报告\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '生成时间: %s\n\n', datestr(now));
        
        % 项目概述
        fprintf(fid, '项目概述:\n');
        fprintf(fid, '本项目旨在优化两个泵站接入管网的最佳节点位置和连接管道长度。\n');
        fprintf(fid, '采用PSO-GA混合优化算法，在满足流量误差≤15%%和水头损失误差≤10%%的\n');
        fprintf(fid, '约束条件下，寻找使目标函数最小的最优解。\n\n');
        
        % 优化参数
        fprintf(fid, '优化参数:\n');
        fprintf(fid, '- 算法: PSO-GA混合优化\n');
        fprintf(fid, '- 泵站1可选节点: [1, 5, 6, 9, 10]\n');
        fprintf(fid, '- 泵站2可选节点: [2, 3, 4, 7]\n');
        fprintf(fid, '- 约束条件: 流量误差≤15%%, 水头损失误差≤10%%\n\n');
        
        % 最优解
        if exist('global_best', 'var') && exist('global_best_fit', 'var')
            allowed_nodes_pump1 = [1, 5, 6, 9, 10];
            allowed_nodes_pump2 = [2, 3, 4, 7];
            final_node1 = allowed_nodes_pump1(round(global_best(1)));
            final_node2 = allowed_nodes_pump2(round(global_best(3)));
            
            fprintf(fid, '最优解:\n');
            fprintf(fid, '- 泵站1最优接入点: 节点%d\n', final_node1);
            fprintf(fid, '- 泵站1最优管道长度: %.2f米\n', global_best(2));
            fprintf(fid, '- 泵站2最优接入点: 节点%d\n', final_node2);
            fprintf(fid, '- 泵站2最优管道长度: %.2f米\n', global_best(4));
            fprintf(fid, '- 目标函数值: %.6f\n\n', global_best_fit);
        end
        
        % 建议
        fprintf(fid, '实施建议:\n');
        fprintf(fid, '1. 根据优化结果进行详细的工程设计\n');
        fprintf(fid, '2. 考虑实际施工条件和成本因素\n');
        fprintf(fid, '3. 进行现场勘测验证优化结果的可行性\n');
        fprintf(fid, '4. 建议进行敏感性分析以评估参数变化的影响\n\n');
        
        fprintf(fid, '报告结束\n');
        fclose(fid);
        
        disp('   综合报告已保存为 comprehensive_optimization_report.txt');
        
    catch ME
        warning('生成综合报告时出错: %s', ME.message);
    end
end

%% 主函数调用
run_optimization();
