% 构造环流量法计算程序的目标函数 (修改版，包含长度作为输入)
% x_4d: 输入的4维决策向量 [node1_idx, length1, node2_idx, length2]
%       node1_idx, node2_idx 是在 allowed_nodes 列表中的索引 (从1开始)
%       length1, length2 是对应的管道长度
% data1-data4: 管网原始数据
% Q_original: 参考流量
% H_loss_original: 参考管段水头损失
% allowed_nodes_pump1, allowed_nodes_pump2: 允许的节点列表
% y:计算得到的目标函数值（包含惩罚项）
function y = myfun1b_mod(x_4d, data1, data2_orig, data3, data4, Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2)

    % --- 解析输入决策变量 ---
    node1_idx = round(x_4d(1)); % 确保是整数索引
    length1   = x_4d(2);
    node2_idx = round(x_4d(3)); % 确保是整数索引
    length2   = x_4d(4);
    % 获取实际接入节点编号
    % 添加边界检查以防索引越界 (虽然优化算法应处理，但增加鲁棒性)
    node1_idx = max(1, min(node1_idx, length(allowed_nodes_pump1)));
    node2_idx = max(1, min(node2_idx, length(allowed_nodes_pump2)));

    actual_node1 = allowed_nodes_pump1(node1_idx);
    actual_node2 = allowed_nodes_pump2(node2_idx);
    
    % --- 创建管网数据的副本并修改 ---
    data2 = data2_orig; % 使用原始数据的副本进行修改
    % 首先找到泵站对应的管段
    pump1_pipe_idx = find(data2(:,2) == 13); % 假设泵站1从节点13出发
    pump2_pipe_idx = find(data2(:,2) == 14); % 假设泵站2从节点14出发
    % 在修改前后打印管段信息
%     fprintf('\n=== 修改前管段信息 ===\n');
%     fprintf('管段1: %d -> %d, 长度: %.1f\n', data2(1,2), data2(1,3), data2(1,6));
%     fprintf('管段5: %d -> %d, 长度: %.1f\n', data2(5,2), data2(5,3), data2(5,6));
    % 修改管段连接节点和长度
%     data2(1, 3) = actual_node1; % 将data2中第一行管段的下游节点改为泵1接入点
%     data2(1, 6) = length1;      % 更新管段1的长度
% 
%     data2(5, 3) = actual_node2; % 将data2中第五行管段的下游节点改为泵2接入点
%     data2(5, 6) = length2;      % 更新管段5的长度
    if ~isempty(pump1_pipe_idx)
        data2(pump1_pipe_idx, 3) = actual_node1; % 修改下游节点
        data2(pump1_pipe_idx, 6) = length1;      % 修改长度
    end

    if ~isempty(pump2_pipe_idx)
        data2(pump2_pipe_idx, 3) = actual_node2;
        data2(pump2_pipe_idx, 6) = length2;
    end
%     fprintf('=== 修改后管段信息 ===\n');
%     fprintf('管段1: %d -> %d, 长度: %.1f\n', data2(1,2), data2(pump1_pipe_idx, 3), data2(pump1_pipe_idx, 6));
%     fprintf('管段5: %d -> %d, 长度: %.1f\n', data2(5,2), data2(pump2_pipe_idx, 3), data2(pump2_pipe_idx, 6));
    % 更新NM矩阵和LD向量，因为后续计算依赖它们
    NM_current = data2(:, 2:3); % NM N×2，管段与上下游节点关联矩阵
    LD = data2(:, 6);           % 更新后的管长向量

    % --- 水力计算参数初始化 ---
    N=data1(1);     % 管段数
    MT=data1(2);    % 计入水源后总节点数
    L=data1(3);     % 实环数
    LX=data1(4);    % 虚实环数
    N_tower=data1(5);% 水塔数
    N_pump=data1(6); % 泵站数
    N_source=N_tower+N_pump;% 水源数
    M=MT-N_source;  % 不含水源的节点数
    C=data1(7);     % 水头参数

    D=data2(:,7);   % 管径
    m_val = data2(1,8); % 假设所有管段的摩阻指数相同

    Q_calc=data2(:,9); % 管段初始流量m^3/s (用于迭代初值)

    if LX~=0
        QX=data3(:,2);% QX,节点流量m^3/s
        HX=data4(:,2); % 水源总水头
        SX=data4(:,3); % 水源出水管虚阻耗系数(s^2/(m^3))
        NQ_pipes=data4(:,4); % 水源出水管管段编号
    end
    EZ=0.05;% 回路水头闭合容差 (此方法中未使用)
    STR=0.5;% 松弛因子

    % --- 节点初始压头 ---
    E_calc=ones(M,1)*70;
    if N_source > 0
        E_calc_sources = HX(1:N_source); 
        E_calc = [E_calc; E_calc_sources];
    end

    % --- 生成MIS,MJS矩阵 ---
    MIS = zeros(M, N);
    MJS = zeros(M, N);
    MM = zeros(M,1);
    for I=1:M
        K=0;
        for J=1:N
            if NM_current(J,1)==I
                K=K+1; MIS(I,K)=J; MJS(I,K)=NM_current(J,2);
            end
            if NM_current(J,2)==I
                K=K+1; MIS(I,K)=-J; MJS(I,K)=NM_current(J,1);
            end
        end
        MM(I)=K;
    end

    % --- 计算管段流量系数R ---
    R_coeff=zeros(N,1);
    for I=1:N
         if LD(I) > 1e-6 
             R_coeff(I)=0.27853*C*D(I)^2.63/LD(I)^0.54;
         else
             R_coeff(I) = Inf; 
         end
    end
    if N_pump~=0 && LX~=0
        for I_pump_src=1:N_pump
             pump_pipe_idx = NQ_pipes(I_pump_src);
             if pump_pipe_idx > 0 && pump_pipe_idx <= N
                 if SX(I_pump_src) > 1e-9 
                    R_coeff(pump_pipe_idx)=1/SX(I_pump_src)^0.5;
                 else
                    R_coeff(pump_pipe_idx) = Inf; 
                 end
             end
        end
    end

    % --- 水力迭代计算 ---
    for KK=1:10
        for I=1:N 
            node_up = NM_current(I,1);
            node_down = NM_current(I,2);
            if node_up > MT || node_down > MT || node_up < 1 || node_down < 1
                 warning('无效的节点号出现在管段 %d: %d -> %d', I, node_up, node_down);
                 y = Inf; 
                 return;
            end
            ET = E_calc(node_up) - E_calc(node_down);

            is_pump_pipe = false;
            if N_pump~=0 && LX~=0
                for I_pump_src=1:N_pump
                    if NQ_pipes(I_pump_src) == I
                        if abs(R_coeff(I)) > 1e-9 
                            Q_calc(I) = R_coeff(I) * abs(ET)^0.5 * sign(ET);
                        else
                            Q_calc(I) = 0; 
                        end
                        is_pump_pipe = true;
                        break;
                    end
                end
            end
            if ~is_pump_pipe
                 if abs(R_coeff(I)) > 1e-9
                     Q_calc(I) = R_coeff(I) * abs(ET)^0.54 * sign(ET);
                 else
                     Q_calc(I) = 0;
                 end
            end
        end

        EQ_balance=zeros(M,1);
        if LX~=0
            for I_node=1:M
                EQ_balance(I_node) = QX(I_node); 
                for J_assoc_pipe=1:MM(I_node)
                    pipe_idx_T = MIS(I_node, J_assoc_pipe);
                    EQ_balance(I_node) = EQ_balance(I_node) - Q_calc(abs(pipe_idx_T)) * sign(pipe_idx_T);
                end
            end
        end

        QJ_derivatives=zeros(N,1);
        for I_pipe=1:N
            node_up = NM_current(I_pipe,1);
            node_down = NM_current(I_pipe,2);
            ET = E_calc(node_up) - E_calc(node_down);
            ET_abs_safe = max(abs(ET), 1e-5); 

            is_pump_pipe_deriv = false;
            if N_pump~=0 && LX~=0
                 for I_pump_src=1:N_pump
                    if NQ_pipes(I_pump_src) == I_pipe
                        if abs(R_coeff(I_pipe)) > 1e-9
                            QJ_derivatives(I_pipe) = R_coeff(I_pipe) * 0.5 * ET_abs_safe^(-0.5);
                        else
                            QJ_derivatives(I_pipe) = 0;
                        end
                        is_pump_pipe_deriv = true;
                        break;
                    end
                end
            end
            if ~is_pump_pipe_deriv
                 if abs(R_coeff(I_pipe)) > 1e-9
                     QJ_derivatives(I_pipe) = R_coeff(I_pipe) * 0.54 * ET_abs_safe^(-0.46);
                 else
                     QJ_derivatives(I_pipe) = 0;
                 end
            end
        end

        AJ_jacobian=zeros(M,M);
        for I_node=1:M
            for J_assoc_pipe=1:MM(I_node)
                NT_pipe_idx = abs(MIS(I_node, J_assoc_pipe));
                NT1_other_node = MJS(I_node, J_assoc_pipe);
                AJ_jacobian(I_node,I_node) = AJ_jacobian(I_node,I_node) + QJ_derivatives(NT_pipe_idx);
                if NT1_other_node <= M && NT1_other_node >= 1 
                    AJ_jacobian(I_node,NT1_other_node) = AJ_jacobian(I_node,NT1_other_node) - QJ_derivatives(NT_pipe_idx);
                end
            end
            if abs(AJ_jacobian(I_node,I_node)) < 1e-9 
                AJ_jacobian(I_node,I_node) = sign(AJ_jacobian(I_node,I_node)) * 1e-9 + 1e11; 
            end
        end

        if LX~=0
            if rcond(AJ_jacobian) < 1e-15
                warning('雅可比矩阵奇异或接近奇异 (组合 node1=%d, L1=%.1f, node2=%d, L2=%.1f)。跳过压头更新。', actual_node1, length1, actual_node2, length2);
                DE_nodes = zeros(M,1); 
            else
                DE_nodes = AJ_jacobian \ EQ_balance;
            end

            max_DE = 5.0; 
            DE_nodes = max(min(DE_nodes, max_DE), -max_DE);

            for I_node=1:M
                E_calc(I_node) = E_calc(I_node) - STR * DE_nodes(I_node);
            end
        end
    end % 结束水力计算迭代 KK

    % --- 约束评价和惩罚项计算 ---
    total_penalty = 0;
    penalty_scale_factor = 1.0; % 调整惩罚因子以平衡目标函数和约束

    % 调试信息（可选）
    debug_mode = false; % 设置为true以启用调试输出
    if debug_mode
        fprintf('调试: 节点组合 [%d, %d], 管长 [%.1f, %.1f]\n', ...
                actual_node1, actual_node2, length1, length2);
    end

    % 1. 流量约束 (保持不变)
    if length(Q_calc) == N && length(Q_original) == N
        flow_violation = 0;
        for i = 1:N
            q_orig = Q_original(i);
            q_calc = Q_calc(i);
            if abs(q_orig) < 1e-6
                if abs(q_calc - q_orig) > 1e-5 
                    flow_violation = flow_violation + (abs(q_calc - q_orig) / 1e-5)^2; 
                end
            else
                flow_error_percent = abs(q_calc - q_orig) / abs(q_orig) * 100;
                if flow_error_percent > 15 
                    flow_violation = flow_violation + (flow_error_percent - 15)^2;
                end
            end
        end
        total_penalty = total_penalty + penalty_scale_factor * flow_violation;
    else
        warning('流量向量长度不匹配');
        total_penalty = total_penalty + penalty_scale_factor * 1e6; 
    end

    % 2. 水头损失约束 (替换原来的节点压头约束)
    H_loss_calc = zeros(N, 1);
    for k_pipe = 1:N
        node_up = NM_current(k_pipe, 1);
        node_down = NM_current(k_pipe, 2);
        % 确保节点索引在 E_calc 范围内 (1 到 MT)
        if node_up >= 1 && node_up <= MT && node_down >= 1 && node_down <= MT
            H_loss_calc(k_pipe) = E_calc(node_up) - E_calc(node_down);
        else
            warning('计算水头损失时，管段 %d 的节点索引 (%d 或 %d) 超出有效范围 [1, %d]', k_pipe, node_up, node_down, MT);
            H_loss_calc(k_pipe) = NaN; % 标记为无效计算
        end
    end

    if length(H_loss_calc) == N && length(H_loss_original) == N
        head_loss_violation = 0;
        for k_pipe = 1:N
            if isnan(H_loss_calc(k_pipe)) % 如果水头损失计算无效
                head_loss_violation = head_loss_violation + (1e3)^2; % 施加一个较大的固定惩罚的平方
                continue;
            end
            hl_orig = H_loss_original(k_pipe);
            hl_calc = H_loss_calc(k_pipe);

            % 定义水头损失约束的容差
            absolute_threshold_hl = 0.2;     % m, 例如: 如果 |hl_orig| < 0.2m
            allowable_absolute_dev_hl = 0.1; % m, 对于较小的 hl_orig，允许的最大绝对偏差
            allowable_relative_error_hl = 10; % 百分比，对于较大的 hl_orig

            if abs(hl_orig) < absolute_threshold_hl
                % 对于原始水头损失值较小的情况，使用绝对偏差
                if abs(hl_calc - hl_orig) > allowable_absolute_dev_hl
                    head_loss_violation = head_loss_violation + (abs(hl_calc - hl_orig) / allowable_absolute_dev_hl)^2;
                end
            else
                % 对于原始水头损失值较大的情况，使用相对误差
                % 防止 hl_orig 为零导致除零错误 (尽管上面已有 absolute_threshold_hl 判断)
                if abs(hl_orig) < 1e-6 % 如果 hl_orig 仍然非常接近零
                     if abs(hl_calc - hl_orig) > allowable_absolute_dev_hl
                        head_loss_violation = head_loss_violation + (abs(hl_calc - hl_orig) / allowable_absolute_dev_hl)^2;
                     end
                else
                    head_loss_error_percent = abs(hl_calc - hl_orig) / abs(hl_orig) * 100;
                    if head_loss_error_percent > allowable_relative_error_hl
                        head_loss_violation = head_loss_violation + (head_loss_error_percent - allowable_relative_error_hl)^2;
                    end
                end
            end
        end
         total_penalty = total_penalty + penalty_scale_factor * head_loss_violation;
    else
         warning('计算得到的水头损失向量长度 (%d) 与参考水头损失向量长度 (%d) 或管段数 (%d) 不匹配', length(H_loss_calc), length(H_loss_original), N);
         total_penalty = total_penalty + penalty_scale_factor * 1e6; % 大惩罚
    end

    % --- 计算原始目标函数值 y_original (与原函数相同, 但使用计算出的E_calc) ---
    if any([1:14, 17] > MT) || actual_node1 > MT || actual_node2 > MT
        warning('目标函数计算中使用了无效的节点索引');
        y = Inf;
        return;
    end

    H_collect.H1=E_calc(13)-E_calc(1); H_collect.H2=E_calc(1)-E_calc(2); H_collect.H3=E_calc(2)-E_calc(3);
    H_collect.H4=E_calc(3)-E_calc(4); H_collect.H5=E_calc(14)-E_calc(4);
    H_collect.H6=E_calc(1)-E_calc(5); H_collect.H7=E_calc(2)-E_calc(6); H_collect.H8=E_calc(3)-E_calc(7);
    H_collect.H9=E_calc(4)-E_calc(8); H_collect.H10=E_calc(5)-E_calc(6);
    H_collect.H11=E_calc(6)-E_calc(7); H_collect.H12=E_calc(7)-E_calc(8); H_collect.H13=E_calc(5)-E_calc(9);
    H_collect.H14=E_calc(6)-E_calc(10); H_collect.H15=E_calc(7)-E_calc(11);
    H_collect.H16=E_calc(12)-E_calc(8); H_collect.H17=E_calc(9)-E_calc(10); H_collect.H18=E_calc(10)-E_calc(11);
    H_collect.H19=E_calc(11)-E_calc(12); H_collect.H20=E_calc(17)-E_calc(12);
    H_collect.H21=E_calc(15)-E_calc(13); H_collect.H22=E_calc(16)-E_calc(14);

    sum_of_specified_Hs = H_collect.H2 + H_collect.H3 + H_collect.H4 + H_collect.H10 + ...
                          H_collect.H6 + H_collect.H7 + H_collect.H8 + H_collect.H9 + ...
                          H_collect.H11 + H_collect.H12 + H_collect.H13 + H_collect.H14 + ...
                          H_collect.H15 + H_collect.H16 + H_collect.H17 + H_collect.H18 + ...
                          H_collect.H19 + H_collect.H20 + H_collect.H21 + H_collect.H22;

    y_original = (E_calc(13) - E_calc(actual_node1)) + (E_calc(14) - E_calc(actual_node2)) + sum_of_specified_Hs;

    % --- 最终目标函数值 ---
%     y = y_original; 
   y = y_original + total_penalty;
    if ~isreal(y) || isnan(y)
        warning('目标函数计算得到非实数值 (NaN/Inf/Complex)');
        y = Inf;
    end
end
