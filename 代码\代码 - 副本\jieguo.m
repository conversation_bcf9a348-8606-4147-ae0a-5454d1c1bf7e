function jieguo
% Data for the plot
x = 1:22;  % 管端 (1 to 22)
y = [0.416377, 0.104436, 0.014855, -0.185395, 0.317899, 0.276707, ...
     0.052965, 0.118754, 0.096172, 0.148269, 0.052522, -0.039828, ...
     0.08003, 0.067858, 0.014385, 0.009251, 0.029872, 0.054843, ...
     -0.035832, 0.081099, 0.421275, 0.320765];  % 流量

% Create the line plot
figure;
plot(x, y, '-o', 'LineWidth', 1.5);
xlabel('管端');
ylabel('流量');
title('管端 vs 流量');
grid on;

% Customize the axis limits if necessary
xlim([1 22]);
ylim([-0.2 0.5]);

% Display the plot
