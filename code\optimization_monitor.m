%% 优化过程监控和可视化脚本
% 该脚本用于监控优化过程并生成可视化结果
% 作者：AI Assistant

function optimization_monitor()
    clc; close all;
    
    %% 1. 设置监控参数
    disp('=== 优化过程监控 ===');
    
    % 创建图形窗口
    fig = figure('Name', '管网优化监控', 'Position', [100, 100, 1200, 800]);
    
    % 子图布局
    subplot(2, 3, 1);
    title('适应度收敛曲线');
    xlabel('迭代次数');
    ylabel('最佳适应度');
    grid on;
    hold on;
    
    subplot(2, 3, 2);
    title('泵站1接入点选择');
    xlabel('迭代次数');
    ylabel('节点编号');
    grid on;
    hold on;
    
    subplot(2, 3, 3);
    title('泵站2接入点选择');
    xlabel('迭代次数');
    ylabel('节点编号');
    grid on;
    hold on;
    
    subplot(2, 3, 4);
    title('泵站1管道长度');
    xlabel('迭代次数');
    ylabel('长度 (m)');
    grid on;
    hold on;
    
    subplot(2, 3, 5);
    title('泵站2管道长度');
    xlabel('迭代次数');
    ylabel('长度 (m)');
    grid on;
    hold on;
    
    subplot(2, 3, 6);
    title('约束违反情况');
    xlabel('迭代次数');
    ylabel('惩罚值');
    grid on;
    hold on;
    
    %% 2. 模拟优化过程（如果没有实际数据）
    if ~exist('fitness_history', 'var')
        disp('生成模拟优化数据...');
        generate_sample_data();
    end
    
    %% 3. 绘制结果
    plot_optimization_results();
    
    %% 4. 生成统计报告
    generate_statistics_report();
    
    disp('监控完成！');
end

%% 生成示例数据（用于演示）
function generate_sample_data()
    global fitness_history node1_history node2_history length1_history length2_history;
    
    % 模拟100代优化过程
    generations = 100;
    
    % 允许的节点
    allowed_nodes_pump1 = [1, 5, 6, 9, 10];
    allowed_nodes_pump2 = [2, 3, 4, 7];
    
    % 初始化历史记录
    fitness_history = zeros(generations, 1);
    node1_history = zeros(generations, 1);
    node2_history = zeros(generations, 1);
    length1_history = zeros(generations, 1);
    length2_history = zeros(generations, 1);
    
    % 模拟收敛过程
    best_fitness = 1000;
    current_node1_idx = 1;
    current_node2_idx = 1;
    current_length1 = 1000;
    current_length2 = 1500;
    
    for gen = 1:generations
        % 模拟适应度改善
        if rand < 0.1  % 10%概率改善
            improvement = rand * 50;
            best_fitness = best_fitness - improvement;
            
            % 可能改变节点选择
            if rand < 0.3
                current_node1_idx = randi(length(allowed_nodes_pump1));
                current_node2_idx = randi(length(allowed_nodes_pump2));
            end
            
            % 微调管道长度
            current_length1 = current_length1 + (rand - 0.5) * 200;
            current_length2 = current_length2 + (rand - 0.5) * 200;
            
            % 确保在合理范围内
            current_length1 = max(500, min(3000, current_length1));
            current_length2 = max(500, min(3000, current_length2));
        end
        
        % 记录历史
        fitness_history(gen) = best_fitness;
        node1_history(gen) = allowed_nodes_pump1(current_node1_idx);
        node2_history(gen) = allowed_nodes_pump2(current_node2_idx);
        length1_history(gen) = current_length1;
        length2_history(gen) = current_length2;
    end
end

%% 绘制优化结果
function plot_optimization_results()
    global fitness_history node1_history node2_history length1_history length2_history;
    
    generations = 1:length(fitness_history);
    
    % 适应度收敛曲线
    subplot(2, 3, 1);
    plot(generations, fitness_history, 'b-', 'LineWidth', 2);
    title('适应度收敛曲线');
    xlabel('迭代次数');
    ylabel('最佳适应度');
    grid on;
    
    % 泵站1接入点选择
    subplot(2, 3, 2);
    plot(generations, node1_history, 'r.-', 'MarkerSize', 8);
    title('泵站1接入点选择');
    xlabel('迭代次数');
    ylabel('节点编号');
    grid on;
    ylim([0, 11]);
    
    % 泵站2接入点选择
    subplot(2, 3, 3);
    plot(generations, node2_history, 'g.-', 'MarkerSize', 8);
    title('泵站2接入点选择');
    xlabel('迭代次数');
    ylabel('节点编号');
    grid on;
    ylim([1, 8]);
    
    % 泵站1管道长度
    subplot(2, 3, 4);
    plot(generations, length1_history, 'm-', 'LineWidth', 1.5);
    title('泵站1管道长度');
    xlabel('迭代次数');
    ylabel('长度 (m)');
    grid on;
    
    % 泵站2管道长度
    subplot(2, 3, 5);
    plot(generations, length2_history, 'c-', 'LineWidth', 1.5);
    title('泵站2管道长度');
    xlabel('迭代次数');
    ylabel('长度 (m)');
    grid on;
    
    % 约束违反情况（模拟）
    subplot(2, 3, 6);
    penalty_history = max(0, fitness_history - min(fitness_history));
    plot(generations, penalty_history, 'k-', 'LineWidth', 1.5);
    title('约束违反情况');
    xlabel('迭代次数');
    ylabel('惩罚值');
    grid on;
end

%% 生成统计报告
function generate_statistics_report()
    global fitness_history node1_history node2_history length1_history length2_history;
    
    if isempty(fitness_history)
        return;
    end
    
    fprintf('\n=== 优化统计报告 ===\n');
    fprintf('总迭代次数: %d\n', length(fitness_history));
    fprintf('初始适应度: %.4f\n', fitness_history(1));
    fprintf('最终适应度: %.4f\n', fitness_history(end));
    fprintf('适应度改善: %.4f (%.2f%%)\n', ...
            fitness_history(1) - fitness_history(end), ...
            (fitness_history(1) - fitness_history(end)) / fitness_history(1) * 100);
    
    fprintf('\n最终解:\n');
    fprintf('泵站1: 节点%d, 管长%.1fm\n', node1_history(end), length1_history(end));
    fprintf('泵站2: 节点%d, 管长%.1fm\n', node2_history(end), length2_history(end));
    
    % 节点选择频率统计
    fprintf('\n节点选择频率:\n');
    allowed_nodes_pump1 = [1, 5, 6, 9, 10];
    allowed_nodes_pump2 = [2, 3, 4, 7];
    
    fprintf('泵站1节点频率:\n');
    for node = allowed_nodes_pump1
        freq = sum(node1_history == node) / length(node1_history) * 100;
        fprintf('  节点%d: %.1f%%\n', node, freq);
    end
    
    fprintf('泵站2节点频率:\n');
    for node = allowed_nodes_pump2
        freq = sum(node2_history == node) / length(node2_history) * 100;
        fprintf('  节点%d: %.1f%%\n', node, freq);
    end
end

%% 主函数调用
if ~exist('OCTAVE_VERSION', 'builtin')
    % MATLAB环境
    optimization_monitor();
else
    % Octave环境
    warning('该脚本在Octave中可能需要调整');
    optimization_monitor();
end
