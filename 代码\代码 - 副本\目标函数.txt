 % 构造环流量法计算程序的目标函数
% x为输入的两个泵站的接入节点编号
% y为计算得到的目标函数值（包含惩罚项）
function y = myfun1b(x, data1, data2, data3, data4, Q_original, E_original) % 新增 Q_original, E_original 参数
    % 原始代码中的参数读取和初始化部分 (data1, NM, NL, LD, D, m, QX, HX, SX, NQ, NX 等)
    % 这部分基本保持不变

    % 修改管段连接：将泵站的出口连接到 x(1) 和 x(2) 指定的节点
    % 假设管段1和管段5是两个泵站的出水管段（需要根据您的实际管网数据确认）
    % 例如：NM(1,2) = x(1); % 假设管段1的下游节点是第一个泵的接入点
    %       NM(5,2) = x(2); % 假设管段5的下游节点是第二个泵的接入点
    % 您的代码中是：
    data2(1,3) = x(1); % 将data2中第一行管段的下游节点改为x(1)
    data2(5,3) = x(2); % 将data2中第五行管段的下游节点改为x(2)
    %管段1长度是data2(1,6),管段2长度是data2(5,6)
    % 更新NM矩阵，因为后续计算依赖NM
    NM_current = data2(:,2:3); % NM N×2，管段与上下游节点关联矩阵

    % --- 水力计算参数初始化 ---
    N=data1(1);     % 管段数
    MT=data1(2);    % 计入水源后总节点数
    L=data1(3);     % 实环数
    LX=data1(4);    % 虚实环数
    N_tower=data1(5);% 水塔数
    N_pump=data1(6); % 泵站数
    N_source=N_tower+N_pump;% 水源数
    M=MT-N_source;  % 不含水源的节点数
    C=data1(7);     % 水头参数

    LD=data2(:,6);  % 管长
    D=data2(:,7);   % 管径
    % m=data2(1,8); % 摩阻指数 (在您的代码中，似乎所有管段共享第一个管段的m值，如果不同，需要循环读取)
    m_val = data2(1,8); % 假设所有管段的摩阻指数相同
    
    Q_calc=data2(:,9); % 管段初始流量m^3/s (用于迭代初值)

    if LX~=0
        QX=data3(:,2);% QX,节点流量m^3/s
        HX=data4(:,2); % 水源总水头
        SX=data4(:,3); % 水源出水管虚阻耗系数(s^2/(m^3))
        NQ_pipes=data4(:,4); % 水源出水管管段编号
        % NX（i,1）,NX(i,2)，虚环泵站始端水源编号和末端水源编号
        % NX_source_start=data4(:,5);
        % NX_source_end=data4(:,6);
    end
    EZ=0.05;% 回路水头闭合容差 (此方法中未使用)
    STR=0.5;% 松弛因子

    % --- 节点初始压头 ---
    E_calc=ones(M,1)*70; % 非水源节点的初始压头设置为70
    if N_source > 0
        E_calc_sources = zeros(N_source,1);
        for I_src=1:N_source
            E_calc_sources(I_src)=HX(I_src); % 水源节点的压头设置为 HX 中的值
        end
        E_calc = [E_calc; E_calc_sources]; % 合并成完整的节点压头向量 E_calc(1:MT)
    end


    % --- 生成MIS,MJS矩阵 ---
    MIS = zeros(M, N); % 预分配以提高效率，大小可能需要调整，或使用cell array
    MJS = zeros(M, N);
    MM = zeros(M,1);
    for I=1:M % 对节点循环
        K=0;
        for J=1:N % 对管段循环
            if NM_current(J,1)==I
                K=K+1;
                MIS(I,K)=J;
                MJS(I,K)=NM_current(J,2);
            end
            if NM_current(J,2)==I
                K=K+1;
                MIS(I,K)=-J;
                MJS(I,K)=NM_current(J,1);
            end
        end
        MM(I)=K;
    end

    % --- 计算管段流量系数R ---
    R_coeff=zeros(N,1);
    for I=1:N
       
        R_coeff(I) = (10.67 * LD(I) / (C^1.852 * D(I)^4.87))^(-1/1.852); % 这是一个更标准的Hazen-Williams流量系数的倒数形式，使得hf = R_hw * Q^1.852，则 Q = (hf/R_hw)^(1/1.852)
                                                                     % 如果您的 R 是直接的 Q = R * hf^0.54, 那么
        R_coeff(I) = ( (10.67 * LD(I)) / (C^1.852 * D(I)^4.87) )^(-0.54); % 假设您的R是这样定义的
                                                                     
        R_coeff(I)=0.27853*C*D(I)^2.63/LD(I)^0.54;

    end
    if N_pump~=0 && LX~=0 % LX~=0才会有SX, NQ_pipes
        for I_pump_src=1:N_pump % 这里应该遍历水源列表中的泵
             % NQ_pipes(I_pump_src) 是第 I_pump_src 个泵水源对应的管段编号
             % SX(I_pump_src) 是该泵的虚阻耗系数
             pump_pipe_idx = NQ_pipes(I_pump_src);
             if pump_pipe_idx > 0 && pump_pipe_idx <= N % 确保管段号有效
                 R_coeff(pump_pipe_idx)=1/SX(I_pump_src)^0.5; % Q = (1/sqrt(SX)) * H^0.5
             end
        end
    end
    % R_coeff=R_coeff'; % 不需要转置，后续使用 R_coeff(I)

    % --- 水力迭代计算 ---
    % KKN=[];
    % EQM=[];
    for KK=1:10 % 迭代次数
        for I=1:N % 对管段循环
            node_up = NM_current(I,1);
            node_down = NM_current(I,2);
            ET = E_calc(node_up) - E_calc(node_down); % 管段I的两端节点的压头差ET
            
            is_pump_pipe = false;
            if N_pump~=0 && LX~=0
                for I_pump_src=1:N_pump
                    if NQ_pipes(I_pump_src) == I
                        Q_calc(I) = R_coeff(I) * abs(ET)^0.5 * sign(ET); % 泵管的流量公式
                        is_pump_pipe = true;
                        break;
                    end
                end
            end
            if ~is_pump_pipe
                Q_calc(I) = R_coeff(I) * abs(ET)^0.54 * sign(ET); % 普通管段的流量公式
            end
        end

        % EQ--->f, J*DE=f；计算节点流量方程 EQ
        EQ_balance=zeros(M,1);
        if LX~=0 % 只有存在节点流量QX时才计算
            for I_node=1:M % 只对非水源节点计算流量平衡
                EQ_balance(I_node)=QX(I_node); % 节点I的给定流量QX(I) (正为流出，负为流入)
                for J_assoc_pipe=1:MM(I_node)
                    pipe_idx_T = MIS(I_node, J_assoc_pipe); % MIS包含的管段号，可正可负
                    % Q_calc(abs(pipe_idx_T)) 是流入该管段的流量
                    % 如果 pipe_idx_T > 0, I_node 是上游节点，水流出I_node, 对平衡方程贡献为 -Q
                    % 如果 pipe_idx_T < 0, I_node 是下游节点，水流入I_node, 对平衡方程贡献为 +Q
                    EQ_balance(I_node) = EQ_balance(I_node) - Q_calc(abs(pipe_idx_T)) * sign(pipe_idx_T); % QX定义为流出为正，流入为负。惯例节点平衡方程是 流入-流出+外部流量=0
                                                                                                   
                EQ_balance(I_node) = QX(I_node); % Re-initialize for each node
                for J_assoc_pipe=1:MM(I_node)
                    pipe_idx_T = MIS(I_node, J_assoc_pipe);
                    EQ_balance(I_node) = EQ_balance(I_node) + Q_calc(abs(pipe_idx_T)) * sign(pipe_idx_T);
                end                                                                                   

            end
        end
        
        % --- 生成系数矩阵AJ ---
        QJ_derivatives=zeros(N,1);
        for I_pipe=1:N % 对管段循环
            node_up = NM_current(I_pipe,1);
            node_down = NM_current(I_pipe,2);
            ET = E_calc(node_up) - E_calc(node_down);
            if(abs(ET) < 1e-5) %避免除以0
                ET=1e-5 * sign(ET);
                if ET == 0, ET = 1e-5; end
            end

            is_pump_pipe_deriv = false;
            if N_pump~=0 && LX~=0
                 for I_pump_src=1:N_pump
                    if NQ_pipes(I_pump_src) == I_pipe
                        QJ_derivatives(I_pipe) = R_coeff(I_pipe) * 0.5 * abs(ET)^(-0.5);
                        is_pump_pipe_deriv = true;
                        break;
                    end
                end
            end
            if ~is_pump_pipe_deriv
                 QJ_derivatives(I_pipe) = R_coeff(I_pipe) * 0.54 * abs(ET)^(-0.46);
            end
        end

        AJ_jacobian=zeros(M,M);% 初始化 AJ 矩阵为 M x M 的零矩阵
        for I_node=1:M % 对非水源节点循环
            for J_assoc_pipe=1:MM(I_node)
                NT_pipe_idx = abs(MIS(I_node, J_assoc_pipe));
                NT1_other_node = MJS(I_node, J_assoc_pipe);
                AJ_jacobian(I_node,I_node) = AJ_jacobian(I_node,I_node) + QJ_derivatives(NT_pipe_idx);
                if NT1_other_node <= M % 如果另一端也是非水源节点
                    AJ_jacobian(I_node,NT1_other_node) = AJ_jacobian(I_node,NT1_other_node) - QJ_derivatives(NT_pipe_idx);
                end
            end
            if AJ_jacobian(I_node,I_node) == 0 % 防止对角线元素为 0
                AJ_jacobian(I_node,I_node) = 1e11; % 或其他处理方式，如检查管网连通性
            end
        end
        
        % EQ_max_dev=max(abs(EQ_balance)); % 最大流量不平衡量
        
        if LX~=0 % 只有存在节点流量QX时才进行节点法迭代求解压头修正
            if rcond(AJ_jacobian) < 1e-15 % 检查条件数，如果矩阵接近奇异
                warning('雅可比矩阵奇异或接近奇异 (组合 %d, %d)。可能无法精确求解DE。', x(1), x(2));
                % 可以选择跳过，或者尝试伪逆 pinv(AJ_jacobian) * EQ_balance
                % y = Inf; % 如果无法求解，返回一个很大的值
                % return;
                DE_nodes = zeros(M,1); % 不修正，或者用伪逆
            else
                DE_nodes = AJ_jacobian \ EQ_balance; % 求解线性方程组
            end
            
            for I_node=1:M % 只更新非水源节点的压头
                E_calc(I_node) = E_calc(I_node) - STR * DE_nodes(I_node);
            end
        end
        % KKN=[KKN,KK];
        % EQM=[EQM,EQ_max_dev];
    end % 结束水力计算迭代 KK

    % --- 约束评价和惩罚项计算 ---
    total_penalty = 0;
    penalty_scale_factor = 1e7; % 较大的惩罚因子以确保约束被遵守

    % 1. 流量约束 (Q_calc vs Q_original)
    if length(Q_calc) == N && length(Q_original) == N
        for i = 1:N
            if abs(Q_original(i)) < 1e-6 % 处理原始流量接近零的情况
                if abs(Q_calc(i) - Q_original(i)) > 1e-5 % 使用绝对误差阈值
                    total_penalty = total_penalty + penalty_scale_factor * (abs(Q_calc(i) - Q_original(i)) / 1e-5); % 惩罚与超出量成比例
                end
            else
                flow_error_percent = abs(Q_calc(i) - Q_original(i)) / abs(Q_original(i)) * 100;
                if flow_error_percent > 15
                    total_penalty = total_penalty + penalty_scale_factor * (flow_error_percent - 15)^2; % 惩罚与超出误差的平方成比例
                end
            end
        end
    else
        warning('流量向量长度不匹配，无法检查流量约束. Q_calc长度: %d, Q_original长度: %d', length(Q_calc), length(Q_original));
        total_penalty = total_penalty + penalty_scale_factor * 1000; % 发生错误，给予大惩罚
    end

    % 2. 节点压头约束 (E_calc vs E_original)
    % 假设 E_original 和 E_calc 均包含所有 MT 个节点
    if length(E_calc) == MT && length(E_original) == MT
        for j = 1:MT % 检查所有节点的压头
            if abs(E_original(j)) < 1e-3 % 处理原始压头接近零的情况
                if abs(E_calc(j) - E_original(j)) > 0.1 % 使用绝对误差阈值 (例如0.1米)
                    total_penalty = total_penalty + penalty_scale_factor * (abs(E_calc(j) - E_original(j)) / 0.1);
                end
            else
                head_error_percent = abs(E_calc(j) - E_original(j)) / abs(E_original(j)) * 100;
                if head_error_percent > 10
                    total_penalty = total_penalty + penalty_scale_factor * (head_error_percent - 10)^2;
                end
            end
        end
    else
        warning('节点压头向量长度不匹配，无法检查压头约束. E_calc长度: %d (MT=%d), E_original长度: %d', length(E_calc), MT, length(E_original));
        total_penalty = total_penalty + penalty_scale_factor * 1000; % 发生错误，给予大惩罚
    end
    
    % --- 计算原始目标函数值 y_original ---
    % E_calc(13) 和 E_calc(14) 等是特定节点的计算后压头
    % x(1) 和 x(2) 是当前方案中泵站的接入节点编号
    
    % 定义 H_collect 结构体来存储各个管段的压降 (水头损失)
    % 注意：这里的节点号需要与您的管网拓扑对应
    % 例如 H_collect.H1 = E_calc( upstream_node_of_pipe1 ) - E_calc( downstream_node_of_pipe1 );
    % 根据您原代码中的 H 定义：
    H_collect.H1=E_calc(13)-E_calc(1); H_collect.H2=E_calc(1)-E_calc(2); H_collect.H3=E_calc(2)-E_calc(3); 
    H_collect.H4=E_calc(3)-E_calc(4); H_collect.H5=E_calc(14)-E_calc(4);
    H_collect.H6=E_calc(1)-E_calc(5); H_collect.H7=E_calc(2)-E_calc(6); H_collect.H8=E_calc(3)-E_calc(7); 
    H_collect.H9=E_calc(4)-E_calc(8); H_collect.H10=E_calc(5)-E_calc(6);
    H_collect.H11=E_calc(6)-E_calc(7); H_collect.H12=E_calc(7)-E_calc(8); H_collect.H13=E_calc(5)-E_calc(9); 
    H_collect.H14=E_calc(6)-E_calc(10); H_collect.H15=E_calc(7)-E_calc(11);
    H_collect.H16=E_calc(12)-E_calc(8); H_collect.H17=E_calc(9)-E_calc(10); H_collect.H18=E_calc(10)-E_calc(11); 
    H_collect.H19=E_calc(11)-E_calc(12); H_collect.H20=E_calc(17)-E_calc(12);
    H_collect.H21=E_calc(15)-E_calc(13); H_collect.H22=E_calc(16)-E_calc(14);

    % 您原始的目标函数中累加的 H 项
    sum_of_specified_Hs = H_collect.H2 + H_collect.H3 + H_collect.H4 + H_collect.H10 + ...
                          H_collect.H6 + H_collect.H7 + H_collect.H8 + H_collect.H9 + ...
                          H_collect.H11 + H_collect.H12 + H_collect.H13 + H_collect.H14 + ...
                          H_collect.H15 + H_collect.H16 + H_collect.H17 + H_collect.H18 + ...
                          H_collect.H19 + H_collect.H20 + H_collect.H21 + H_collect.H22;
    
    % 假设 E_calc(13) 和 E_calc(14) 是两个泵站的源头参考压头（例如吸水井水位）
    % 而 x(1) 和 x(2) 是泵站接入管网的节点，E_calc(x(1)) 和 E_calc(x(2)) 是接入点的压头
    % 目标是优化与这两个泵相关的某种压降或压差组合
    y_original = (E_calc(13) - E_calc(x(1))) + (E_calc(14) - E_calc(x(2))) + sum_of_specified_Hs;
    
    % 最终目标函数值 = 原始目标值 + 总惩罚项
    y = y_original + total_penalty;

end
