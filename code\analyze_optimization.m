%% 管网泵站接入点优化分析脚本
% 该脚本用于分析和验证优化结果
% 作者：AI Assistant
% 日期：2024

clc; clear; close all;

%% 1. 数据加载和验证
disp('=== 管网泵站接入点优化分析 ===');
disp('1. 加载数据...');

try
    data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
    data2_orig = xlsread('许仕荣87页管网.xlsx', '管段数据');
    data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
    data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
    disp('   数据加载成功！');
catch ME
    error('数据加载失败: %s', ME.message);
end

%% 2. 显示管网基本信息
fprintf('\n2. 管网基本信息:\n');
fprintf('   管段数: %d\n', data1(1));
fprintf('   总节点数: %d\n', data1(2));
fprintf('   实环数: %d\n', data1(3));
fprintf('   虚实环数: %d\n', data1(4));
fprintf('   水塔数: %d\n', data1(5));
fprintf('   泵站数: %d\n', data1(6));

%% 3. 定义优化参数
allowed_nodes_pump1 = [1, 5, 6, 9, 10];
allowed_nodes_pump2 = [2, 3, 4, 7];
length_ranges_pump1 = [
    1000, 1000;  % Node 1
    380, 800;    % Node 5
    800, 1500;   % Node 6
    1350, 2000;  % Node 9
    1500, 3000   % Node 10
];
length_ranges_pump2 = [
    1750, 2500;  % Node 2
    400, 800;    % Node 3
    240, 240;    % Node 4
    1200, 2000   % Node 7
];

fprintf('\n3. 优化参数设置:\n');
fprintf('   泵站1可选节点: [%s]\n', num2str(allowed_nodes_pump1));
fprintf('   泵站2可选节点: [%s]\n', num2str(allowed_nodes_pump2));

%% 4. 参考数据
Q_original = [
    0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
    0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
    0.4213; 0.3208
];

H_loss_original = [
    12.2700; 2.0000; -0.3900; -2.0900; 1.8000; 1.4700; 1.7600; 4.9600;
    3.0900; 2.1200; 3.6100; -5.4000; 2.9600; 2.4600; 2.3700; 1.4400;
    1.7900; 3.5200; -8.6600; 0.5100; 5.1600; 12.0500
];

fprintf('\n4. 约束条件:\n');
fprintf('   流量误差限制: ≤ 15%%\n');
fprintf('   水头损失误差限制: ≤ 10%%\n');

%% 5. 运行优化算法
disp(sprintf('\n5. 开始运行优化算法...'));
disp('   算法: PSO + GA 混合优化');
disp('   这可能需要几分钟时间...');

% 记录开始时间
tic;

% 运行主优化程序
try
    run('main1bpsogad.m');
    optimization_time = toc;
    fprintf('   优化完成！用时: %.2f 秒\n', optimization_time);
catch ME
    fprintf('   优化过程中出现错误: %s\n', ME.message);
    return;
end

%% 6. 结果分析
disp(sprintf('\n6. 结果分析:'));
if exist('global_best', 'var') && exist('global_best_fit', 'var')
    final_node1 = allowed_nodes_pump1(round(global_best(1)));
    final_len1 = global_best(2);
    final_node2 = allowed_nodes_pump2(round(global_best(3)));
    final_len2 = global_best(4);
    
    fprintf('   最优解:\n');
    fprintf('     泵站1: 节点%d, 管长%.2fm\n', final_node1, final_len1);
    fprintf('     泵站2: 节点%d, 管长%.2fm\n', final_node2, final_len2);
    fprintf('     目标函数值: %.6f\n', global_best_fit);
    
    % 验证约束条件
    fprintf('\n   约束验证:\n');
    test_solution = [round(global_best(1)), final_len1, round(global_best(3)), final_len2];
    fitness_value = myfun1b_mod(test_solution, data1, data2_orig, data3, data4, ...
                               Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);
    
    if fitness_value < 1e6  % 如果没有大的惩罚项
        fprintf('     ✓ 约束条件满足\n');
    else
        fprintf('     ✗ 约束条件不满足\n');
    end
else
    fprintf('   优化结果不可用\n');
end

%% 7. 生成报告
disp(sprintf('\n7. 生成优化报告...'));
generate_optimization_report();

disp(sprintf('\n=== 分析完成 ==='));

%% 辅助函数：生成优化报告
function generate_optimization_report()
    try
        % 创建报告文件
        fid = fopen('optimization_report.txt', 'w');
        if fid == -1
            warning('无法创建报告文件');
            return;
        end
        
        fprintf(fid, '管网泵站接入点优化报告\n');
        fprintf(fid, '生成时间: %s\n\n', datestr(now));
        
        if exist('global_best', 'var')
            fprintf(fid, '优化结果:\n');
            fprintf(fid, '泵站1最优接入点: 节点%d\n', allowed_nodes_pump1(round(global_best(1))));
            fprintf(fid, '泵站1最优管长: %.2f米\n', global_best(2));
            fprintf(fid, '泵站2最优接入点: 节点%d\n', allowed_nodes_pump2(round(global_best(3))));
            fprintf(fid, '泵站2最优管长: %.2f米\n', global_best(4));
            fprintf(fid, '最优目标函数值: %.6f\n', global_best_fit);
        end
        
        fclose(fid);
        disp('   报告已保存到 optimization_report.txt');
    catch
        warning('报告生成失败');
    end
end
