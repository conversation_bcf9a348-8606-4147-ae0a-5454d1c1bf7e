@echo off
cd /d "d:\projects\汞站180\code"
echo Starting MATLAB optimization system...
echo Current directory: %CD%

REM Check if MATLAB is available
where matlab >nul 2>&1
if %errorlevel% neq 0 (
    echo MATLAB not found in PATH. Please ensure MATLAB is installed and in PATH.
    pause
    exit /b 1
)

echo Running system test...
matlab -nodesktop -nosplash -r "try; test_system; catch ME; disp(['Error: ' ME.message]); disp(ME.stack); end; pause(3); exit"

echo.
echo Test completed. Press any key to run full optimization...
pause

echo Running full optimization...
matlab -nodesktop -nosplash -r "try; run_optimization; catch ME; disp(['Error: ' ME.message]); disp(ME.stack); end; pause(5); exit"

echo.
echo Optimization completed. Check the generated files.
pause
