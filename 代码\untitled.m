clc;
clear;
close all;

% 读取 Excel 文件中的数据，只需执行一次
data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
data2 = xlsread('许仕荣87页管网.xlsx','管段数据');
data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
N = 11; % 可选择的节点总数

% 考虑管长
LLL = [1000, 3000; 1300, 2000; 1500, 800; 2000, 240; 700, 3500; 1400, 3000; 1900, 2000; 2500, 1000; 2000, 4000; 3500, 3500; 4000, 1800];
LL1 = zeros(50, 1);
LL2 = zeros(50, 1);

% 参考节点（1,5）的流量和压头（可以在算法运行前设定）
Q_ref = [data3(1), data3(5)];  % 参考节点流量（从节点流量表中获取）
E_ref = [data2(1, 2), data2(5, 2)];  % 参考节点压头（假设压头在节点数据中是第二列）

% 粒子群算法相关参数
np = 100;  % 种群个数
gen = 100; % 迭代次数
dim = 2;   % 决策变量的维度
c1 = 1;    % 学习因子
c2 = 1;
w_max = 0.9;   % 惯性权重
w_min = 0.1;
v_max = 0.3 * N;  % 粒子的速度限制
v_min = -v_max;

%% 初始化粒子集合和速度
for i = 1:np
    x(i, :) = randi([1, N], 1, dim);  % 在 1 到 N 范围内随机生成初始解
    LL1(i, 1) = LLL(x(i, 1), 1);  % 考虑管长
    LL2(i, 1) = LLL(x(i, 2), 2);  % 考虑管长
end
v = (rand(np, dim) - 0.5) * 2 * v_max;  % 随机初始化速度，并添加扰动

%% 计算初始化 个体最优 和 全局最优
% 个体最优
pbest = x;
for i = 1:np
    pbest_fit(i, 1) = myfun1b(x(i, :), LL1(i, 1), LL2(i, 1), data1, data2, data3, data4);  % 存储个体最优值
end
% 全局最优
[global_best_fit, idx] = min(pbest_fit);
global_best = pbest(idx(1), :);

%% 模拟退火参数
T_max = 100;   % 初始温度
T_min = 1e-3;  % 最终温度
alpha = 0.95;  % 温度衰减系数

%% 迭代计算
for t = 1:gen
    % 计算动态权重系数
    w = w_max - (w_max - w_min) * t / gen;
    
    for i = 1:np
        % 更新粒子的速度
        v(i, :) = w * v(i, :) + c1 * rand * (pbest(i, :) - x(i, :)) + c2 * rand * (global_best - x(i, :));
        % 对速度超出边界进行限制
        v(i, :) = max(min(v(i, :), v_max), v_min);

        % 更新粒子的位置，并确保为整数
        x(i, :) = x(i, :) + round(v(i, :));

        % 检查位置边界
        x(i, :) = max(min(x(i, :), N), 1);

        % 防止重复：如果位置有重复值，重新生成
        while length(x(i, :)) ~= length(unique(x(i, :)))
            x(i, :) = randperm(N, dim);
        end
    end

    % 计算个体历史最优和全局最优
    for i = 1:np
        fitness = myfun1b(x(i, :), LL1(i, 1), LL2(i, 1), data1, data2, data3, data4);  % 考虑管长
        if fitness < pbest_fit(i)
            pbest_fit(i) = fitness;
            pbest(i, :) = x(i, :);
        end
        if fitness < global_best_fit
            global_best_fit = fitness;
            global_best = x(i, :);
        end
    end
    
    % 模拟退火接受准则
    delta_fit = fitness - global_best_fit;
    if delta_fit < 0 || rand() < exp(-delta_fit / T_max)
        global_best = x(i, :);  % 更新全局最优
        global_best_fit = fitness;
    end
    
    % 温度衰减
    T_max = T_max * alpha;

    % 计算误差
    flow_error = abs(myfun1b(global_best, LL1, LL2, data1, data2, data3, data4) - Q_ref);  % 计算流量误差
    head_error = abs(global_best - E_ref);  % 计算压头误差
    disp(['第', num2str(t), '代计算完成。 最优选点为 ', num2str(global_best), '，适应度值为 ', num2str(global_best_fit)]);
    disp(['流量误差：', num2str(flow_error)]);
    disp(['压头误差：', num2str(head_error)]);
end

% 绘制收敛曲线
figure;
plot(Convergence_curve);

% 如果需要显示误差
disp(['总流量误差：', num2str(sum(flow_error))]);
disp(['总压头误差：', num2str(sum(head_error))]);
