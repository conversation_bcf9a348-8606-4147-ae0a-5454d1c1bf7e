# 管网泵站接入点优化系统

## 项目概述

本项目是一个管网泵站接入点优化系统，旨在为两个泵站找到最优的管网接入节点和连接管道长度。系统采用PSO-GA混合优化算法，在满足流量误差≤15%和水头损失误差≤10%的约束条件下，寻找使目标函数最小的最优解。

## 项目结构

```
汞站180/
├── code/
│   ├── main1bpsogad.m              # 主优化算法（PSO-GA混合）
│   ├── myfun1b_mod.m               # 目标函数（修改版）
│   ├── run_optimization.m          # 主运行脚本
│   ├── analyze_optimization.m      # 优化分析脚本
│   ├── optimization_monitor.m      # 优化监控脚本
│   ├── 许仕荣87页管网.xlsx         # 管网数据文件
│   ├── 管网结构.png               # 管网结构图
│   ├── 优化函数.txt               # 优化函数说明
│   └── 目标函数.txt               # 目标函数说明
└── README.md                       # 本文件
```

## 核心功能

### 1. 优化算法
- **算法类型**: PSO（粒子群优化）+ GA（遗传算法）混合优化
- **优化变量**: 
  - 泵站1接入节点选择（离散变量）
  - 泵站1连接管道长度（连续变量）
  - 泵站2接入节点选择（离散变量）
  - 泵站2连接管道长度（连续变量）

### 2. 约束条件
- 流量误差 ≤ 15%
- 水头损失误差 ≤ 10%

### 3. 可选接入点
- **泵站1**: 节点 [1, 5, 6, 9, 10]
- **泵站2**: 节点 [2, 3, 4, 7]

## 使用方法

### 快速开始

1. **运行完整优化分析**:
   ```matlab
   cd code
   run_optimization
   ```

2. **仅运行优化算法**:
   ```matlab
   cd code
   main1bpsogad
   ```

3. **分析已有结果**:
   ```matlab
   cd code
   analyze_optimization
   ```

4. **监控优化过程**:
   ```matlab
   cd code
   optimization_monitor
   ```

### 详细步骤

#### 步骤1: 环境准备
确保MATLAB环境中包含以下文件：
- `许仕荣87页管网.xlsx` - 管网基础数据
- 所有.m文件

#### 步骤2: 参数设置
主要参数在 `main1bpsogad.m` 中设置：
```matlab
np = 100;         % 种群数量
gen = 200;        % 最大迭代次数
w = 0.9;          % PSO惯性权重
c1 = 2.0;         % PSO个体学习因子
c2 = 2.0;         % PSO社会学习因子
```

#### 步骤3: 运行优化
执行 `run_optimization.m` 将自动完成：
- 数据验证
- 优化算法运行
- 结果分析
- 可视化生成
- 报告生成

## 输出文件

运行完成后将生成以下文件：

### 数据文件
- `optimization_results.mat` - MATLAB数据文件，包含所有优化结果
- `optimization_detailed_report.txt` - 详细优化报告
- `comprehensive_optimization_report.txt` - 综合分析报告

### 图表文件
- `optimization_analysis.png` - 优化分析图表
- MATLAB图形窗口显示收敛曲线

## 结果解读

### 最优解格式
```
泵站1: 节点X, 管长Y.YYm
泵站2: 节点X, 管长Y.YYm
目标函数值: X.XXXXXX
```

### 约束验证
- ✓ 表示约束条件满足
- ✗ 表示约束条件不满足，需要调整参数

### 收敛分析
- 初始适应度 vs 最终适应度
- 改善幅度（绝对值和百分比）
- 实际迭代次数

## 算法改进

### 已实现的改进
1. **收敛监控**: 添加早停机制，避免无效迭代
2. **参数优化**: 调整PSO和GA参数以提高收敛性能
3. **约束处理**: 改进惩罚函数，更好地处理约束违反
4. **结果保存**: 自动保存优化历史和详细报告

### 可进一步改进的方向
1. **多目标优化**: 考虑成本、可靠性等多个目标
2. **敏感性分析**: 分析参数变化对结果的影响
3. **并行计算**: 利用并行计算加速优化过程
4. **自适应参数**: 根据收敛情况动态调整算法参数

## 故障排除

### 常见问题

1. **数据文件找不到**
   - 确保 `许仕荣87页管网.xlsx` 在code目录下
   - 检查文件名是否正确

2. **优化不收敛**
   - 增加迭代次数 `gen`
   - 调整种群大小 `np`
   - 检查约束条件设置

3. **约束条件不满足**
   - 调整惩罚因子
   - 检查数据的合理性
   - 放宽约束条件进行测试

4. **内存不足**
   - 减少种群大小
   - 减少迭代次数
   - 清理不必要的变量

## 技术支持

如遇到问题，请检查：
1. MATLAB版本兼容性
2. 数据文件完整性
3. 算法参数设置
4. 系统资源是否充足

## 更新日志

### v2.0 (当前版本)
- 添加收敛监控和早停机制
- 改进约束处理和惩罚函数
- 增加详细的结果分析和可视化
- 添加综合运行脚本和报告生成

### v1.0 (原始版本)
- 基本PSO-GA混合优化算法
- 基础约束处理
- 简单结果输出
