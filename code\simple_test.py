#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("=" * 50)
print("管网优化系统测试")
print("=" * 50)

# 测试基本功能
import sys
print(f"Python版本: {sys.version}")

try:
    import numpy as np
    print("✓ NumPy可用")
except ImportError:
    print("✗ NumPy不可用")

try:
    import pandas as pd
    print("✓ Pandas可用")
except ImportError:
    print("✗ Pandas不可用")

try:
    import matplotlib.pyplot as plt
    print("✓ Matplotlib可用")
except ImportError:
    print("✗ Matplotlib不可用")

# 测试Excel文件
import os
excel_file = "许仕荣87页管网.xlsx"
if os.path.exists(excel_file):
    print(f"✓ 找到Excel文件: {excel_file}")
else:
    print(f"✗ 未找到Excel文件: {excel_file}")

print("\n测试完成！")
print("如果所有依赖都可用，可以运行完整的优化程序。")
