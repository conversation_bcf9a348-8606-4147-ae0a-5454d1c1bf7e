%% 系统测试脚本
% 用于快速验证优化系统是否正常工作
% 使用较少的迭代次数进行快速测试

function test_system()
    clc; clear; close all;
    
    disp('=== 管网优化系统测试 ===');
    
    %% 1. 数据文件检查
    disp('1. 检查数据文件...');
    required_files = {'许仕荣87页管网.xlsx', 'main1bpsogad.m', 'myfun1b_mod.m'};
    
    for i = 1:length(required_files)
        if exist(required_files{i}, 'file')
            fprintf('   ✓ %s\n', required_files{i});
        else
            fprintf('   ✗ %s (缺失)\n', required_files{i});
            error('缺少必要文件: %s', required_files{i});
        end
    end
    
    %% 2. 数据加载测试
    disp('2. 测试数据加载...');
    try
        data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
        data2_orig = xlsread('许仕荣87页管网.xlsx', '管段数据');
        data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
        data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
        
        fprintf('   ✓ 管网基本参数: %dx%d\n', size(data1));
        fprintf('   ✓ 管段数据: %dx%d\n', size(data2_orig));
        fprintf('   ✓ 节点流量: %dx%d\n', size(data3));
        fprintf('   ✓ 水源数据: %dx%d\n', size(data4));
    catch ME
        error('数据加载失败: %s', ME.message);
    end
    
    %% 3. 目标函数测试
    disp('3. 测试目标函数...');
    
    % 参考数据
    Q_original = [
        0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
        0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
        0.4213; 0.3208
    ];
    
    H_loss_original = [
        12.2700; 2.0000; -0.3900; -2.0900; 1.8000; 1.4700; 1.7600; 4.9600;
        3.0900; 2.1200; 3.6100; -5.4000; 2.9600; 2.4600; 2.3700; 1.4400;
        1.7900; 3.5200; -8.6600; 0.5100; 5.1600; 12.0500
    ];
    
    allowed_nodes_pump1 = [1, 5, 6, 9, 10];
    allowed_nodes_pump2 = [2, 3, 4, 7];
    
    % 测试几个解
    test_solutions = [
        [1, 1000, 1, 1750];  % 泵1->节点1, 泵2->节点2
        [3, 800, 2, 400];    % 泵1->节点6, 泵2->节点3
        [5, 1500, 4, 240];   % 泵1->节点10, 泵2->节点4
    ];
    
    fprintf('   测试解的目标函数值:\n');
    for i = 1:size(test_solutions, 1)
        try
            fitness = myfun1b_mod(test_solutions(i, :), data1, data2_orig, data3, data4, ...
                                 Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);
            node1 = allowed_nodes_pump1(test_solutions(i, 1));
            node2 = allowed_nodes_pump2(test_solutions(i, 3));
            fprintf('     解%d: P1->N%d(L=%.0f), P2->N%d(L=%.0f) => %.4f\n', ...
                    i, node1, test_solutions(i, 2), node2, test_solutions(i, 4), fitness);
        catch ME
            fprintf('     解%d: 计算失败 - %s\n', i, ME.message);
        end
    end
    
    %% 4. 快速优化测试
    disp('4. 运行快速优化测试 (10代)...');
    
    % 临时修改参数进行快速测试
    original_gen = 200;  % 假设原始值
    original_np = 100;   % 假设原始值
    
    try
        % 运行快速测试版本的优化
        run_quick_optimization();
        disp('   ✓ 快速优化测试完成');
    catch ME
        fprintf('   ✗ 快速优化测试失败: %s\n', ME.message);
    end
    
    %% 5. 系统状态报告
    disp('5. 系统状态报告:');
    fprintf('   MATLAB版本: %s\n', version);
    fprintf('   当前目录: %s\n', pwd);
    fprintf('   可用内存: %.1f MB\n', get_available_memory());
    
    disp('=== 系统测试完成 ===');
    disp('如果所有测试都通过，系统可以正常运行完整优化。');
end

%% 快速优化函数
function run_quick_optimization()
    % 设置快速测试参数
    np = 20;          % 小种群
    gen = 10;         % 少迭代
    dim = 4;
    
    % 允许的节点
    allowed_nodes_pump1 = [1, 5, 6, 9, 10];
    allowed_nodes_pump2 = [2, 3, 4, 7];
    
    % 管道长度范围
    length_ranges_pump1 = [
        1000, 1000;  % Node 1
        380, 800;    % Node 5
        800, 1500;   % Node 6
        1350, 2000;  % Node 9
        1500, 3000   % Node 10
    ];
    length_ranges_pump2 = [
        1750, 2500;  % Node 2
        400, 800;    % Node 3
        240, 240;    % Node 4
        1200, 2000   % Node 7
    ];
    
    % 加载数据
    data1 = xlsread('许仕荣87页管网.xlsx', '管网基本参数');
    data2_orig = xlsread('许仕荣87页管网.xlsx', '管段数据');
    data3 = xlsread('许仕荣87页管网.xlsx', '节点流量');
    data4 = xlsread('许仕荣87页管网.xlsx', '水源数据');
    
    Q_original = [
        0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
        0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
        0.4213; 0.3208
    ];
    
    H_loss_original = [
        12.2700; 2.0000; -0.3900; -2.0900; 1.8000; 1.4700; 1.7600; 4.9600;
        3.0900; 2.1200; 3.6100; -5.4000; 2.9600; 2.4600; 2.3700; 1.4400;
        1.7900; 3.5200; -8.6600; 0.5100; 5.1600; 12.0500
    ];
    
    % 初始化种群
    x = zeros(np, dim);
    for i = 1:np
        x(i, 1) = randi(length(allowed_nodes_pump1));
        x(i, 3) = randi(length(allowed_nodes_pump2));
        
        % 设置管道长度
        node1_idx = x(i, 1);
        node2_idx = x(i, 3);
        x(i, 2) = length_ranges_pump1(node1_idx, 1) + ...
                  rand * (length_ranges_pump1(node1_idx, 2) - length_ranges_pump1(node1_idx, 1));
        x(i, 4) = length_ranges_pump2(node2_idx, 1) + ...
                  rand * (length_ranges_pump2(node2_idx, 2) - length_ranges_pump2(node2_idx, 1));
    end
    
    % 计算初始适应度
    best_fitness = inf;
    best_solution = [];
    
    for i = 1:np
        fitness = myfun1b_mod(x(i, :), data1, data2_orig, data3, data4, ...
                             Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);
        if fitness < best_fitness
            best_fitness = fitness;
            best_solution = x(i, :);
        end
    end
    
    fprintf('     初始最佳适应度: %.4f\n', best_fitness);
    
    % 简单的随机搜索（模拟优化过程）
    for t = 1:gen
        % 随机改进
        for i = 1:np
            % 小幅度随机变化
            new_x = x(i, :);
            if rand < 0.3  % 30%概率改变节点
                new_x(1) = randi(length(allowed_nodes_pump1));
                new_x(3) = randi(length(allowed_nodes_pump2));
            end
            
            % 微调长度
            new_x(2) = new_x(2) + (rand - 0.5) * 100;
            new_x(4) = new_x(4) + (rand - 0.5) * 100;
            
            % 确保在范围内
            node1_idx = round(new_x(1));
            node2_idx = round(new_x(3));
            if node1_idx >= 1 && node1_idx <= length(allowed_nodes_pump1)
                new_x(2) = max(length_ranges_pump1(node1_idx, 1), ...
                              min(length_ranges_pump1(node1_idx, 2), new_x(2)));
            end
            if node2_idx >= 1 && node2_idx <= length(allowed_nodes_pump2)
                new_x(4) = max(length_ranges_pump2(node2_idx, 1), ...
                              min(length_ranges_pump2(node2_idx, 2), new_x(4)));
            end
            
            % 评估新解
            try
                new_fitness = myfun1b_mod(new_x, data1, data2_orig, data3, data4, ...
                                         Q_original, H_loss_original, allowed_nodes_pump1, allowed_nodes_pump2);
                if new_fitness < best_fitness
                    best_fitness = new_fitness;
                    best_solution = new_x;
                    x(i, :) = new_x;
                end
            catch
                % 如果新解无效，保持原解
            end
        end
    end
    
    fprintf('     最终最佳适应度: %.4f\n', best_fitness);
    
    if ~isempty(best_solution)
        node1 = allowed_nodes_pump1(round(best_solution(1)));
        node2 = allowed_nodes_pump2(round(best_solution(3)));
        fprintf('     最佳解: P1->N%d(L=%.1f), P2->N%d(L=%.1f)\n', ...
                node1, best_solution(2), node2, best_solution(4));
    end
end

%% 获取可用内存（简化版）
function mem_mb = get_available_memory()
    try
        if ispc
            [~, sys_info] = memory;
            mem_mb = sys_info.PhysicalMemory.Available / 1024 / 1024;
        else
            mem_mb = NaN;  % 非Windows系统
        end
    catch
        mem_mb = NaN;
    end
end

%% 主函数调用
test_system();
