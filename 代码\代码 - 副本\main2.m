%% 遍历代码
clear
close all

N = 11;                                  %可以选择的节点
result = zeros(N*(N-1),3);               %前两列为两个泵的编号，最后一列为计算得到的压降之和

m = 0;
rank1 = 1:1:N;
for i=1:N
    rank2 = rank1;
    rank2(i) = [];
    for j=1:N-1
        m = m+1;
        result(m,1) = rank1(i);
        result(m,2) = rank2(j);
        result(m,3) = myfun([rank1(i) rank2(j)]);
        disp(['遍历到第',num2str(m),'个'])
    end
end

disp([min(result(:,3))])