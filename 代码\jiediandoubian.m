
clear
close all

% --- 0. 数据加载 ---
disp('--- 开始加载数据 ---');
data1 = xlsread('许仕荣87页管网.xlsx','管网基本参数');
data2_orig = xlsread('许仕荣87页管网.xlsx','管段数据'); % 保存原始管段数据
data3 = xlsread('许仕荣87页管网.xlsx','节点流量');
data4 = xlsread('许仕荣87页管网.xlsx','水源数据');
disp('数据加载成功。');

% --- 提取基本参数 (在循环外定义，因为它们不变) ---
N=data1(1);             % 管段数
MT=data1(2);            % 计入水源后总节点数
L_loop=data1(3);        % 实环数 (未使用，原变量L与管长L易混淆，改名)
LX=data1(4);            % 虚实环数 (决定是否进行水力计算的关键参数)
N_tower=data1(5);       % 水塔数
N_pump=data1(6);        % 泵站数
N_source=N_tower+N_pump;% 水源数
M=MT-N_source;          % 不含水源的节点数 (普通节点数)
C_coeff=data1(7);       % 水头参数 (海曾威廉系数C)
m_exp=data2_orig(1,8);  % 摩阻指数 (全局唯一, 通常为1.85或2)

% --- 如果LX~=0，读取相关数据 (在循环外定义) ---
if LX~=0
    QX=data3(:,2);      % QX,各普通节点流量m^3/s (M x 1)
    HX=data4(:,2);      % 各水源总水头 (N_source x 1)
    SX=data4(:,3);      % 各水源出水管虚阻耗系数(s^2/(m^3)) (N_source x 1)
    NQ_pump_pipes=data4(:,4); % 各水源出水管管段编号 (N_source x 1)
else
    % 如果LX=0，通常意味着不进行完整的牛顿迭代水力计算，或者是一种特殊情况
    % 初始化这些变量以避免错误，但它们在LX=0时可能不被严格使用
    QX=zeros(M,1);
    HX=zeros(N_source,1);
    SX=ones(N_source,1); % 虚阻耗系数设为1，实际影响取决于R_coeffs的计算
    NQ_pump_pipes=zeros(N_source,1); % 假设没有特定的泵管
end

% --- 检查是否有足够的泵进行双泵操作 ---
if N_pump < 2
    error('错误：定义的泵站数量 N_pump 小于2，无法执行双泵参数化搜索。请检查Excel数据。');
end
% 获取两个泵对应的管段编号 (假设第一个水源是泵1，第二个是泵2)
pump1_pipe_idx = NQ_pump_pipes(1);
pump2_pipe_idx = NQ_pump_pipes(2);
% 获取两个泵管的原始起点节点 (通常是水源节点 M+1, M+2 等)
pump1_original_start_node = data2_orig(pump1_pipe_idx, 2);
pump2_original_start_node = data2_orig(pump2_pipe_idx, 2);


EZ=0.05;                % 回路水头闭合容差 (未使用)
STR=0.5;                % 松弛因子 (用于节点水压修正)

% --- 参考流量与压头 (用于最终校验，来自原代码) ---
Q_ref = [
    0.4164; 0.1044; 0.0149; -0.1854; 0.3179; 0.2767; 0.0530; 0.1188; 0.0962; 0.1483;
    0.0525; -0.0398; 0.0800; 0.0679; 0.0144; 0.0093; 0.0299; 0.0548; -0.0358; 0.0811;
    0.4213; 0.3208
];
E_ref = [
    74.576428; 72.573442; 72.167468; 75.159936; 73.109308; 70.810565; 67.205452;
    72.065420; 70.142379; 68.351104; 64.834299; 73.496291; 86.845284; 76.950363;
    92.000000; 89.000000; 74.000000
];

% --- 新增：泵的参数化搜索范围 ---
% 泵1 (对应 NQ_pump_pipes(1) 管段)
pump1_connection_nodes = [5, 6, 9, 10]; % 可选的接入节点列表
pump1_length_min = 200; % 管长最小搜索值 (m)
pump1_length_max = 2300; % 管长最大搜索值 (m)
pump1_length_step = 50; % 管长搜索步长 (m)

% 泵2 (对应 NQ_pump_pipes(2) 管段)
pump2_connection_nodes = [2, 3, 7];   % 可选的接入节点列表
pump2_length_min = 200; % 管长最小搜索值 (m)
pump2_length_max = 2300; % 管长最大搜索值 (m)
pump2_length_step = 50; % 管长搜索步长 (m)

% --- 约束与目标 ---
target_sum_Hs = 49;         % 特定管段压降和的目标值
sum_Hs_tolerance = 20;      % 目标值的容许误差
flow_deviation_percent = 15;% 管段流量与参考值允许的最大偏差百分比
head_deviation_percent = 10;% 节点水压与参考值允许的最大偏差百分比

max_hydraulic_iterations = 200; % 水力计算最大迭代次数
convergence_threshold_EQ = 1e-4;% 水力计算收敛阈值 (节点流量不平衡量)

% --- 结果存储 ---
suitable_solutions = []; % 用于存储所有满足条件的解
best_solution.pump1_node = NaN;
best_solution.pump2_node = NaN;
best_solution.pump1_length = NaN;
best_solution.pump2_length = NaN;
best_solution.sum_Hs = NaN;
best_solution.metric_sum_Hs_diff = Inf; % 用于比较sum_Hs与目标的差距，越小越好
best_solution.Q_calculated = [];
best_solution.E_calculated = [];
best_solution.converged = false;
best_solution.max_EQ_imbalance = NaN;

% --- 新增：用于追踪所有测试组合中，产生的最低总压降和对应的参数 ---
min_overall_sum_Hs_solution.value = Inf;
min_overall_sum_Hs_solution.pump1_node = NaN;
min_overall_sum_Hs_solution.pump2_node = NaN;
min_overall_sum_Hs_solution.pump1_length = NaN;
min_overall_sum_Hs_solution.pump2_length = NaN;
% --- 新增结束 ---

disp('--- 开始参数化搜索 ---');
fprintf('泵1管段号: %d, 原始起点: %d. 搜索接入节点: %s, 管长范围: [%.1f-%.1f], 步长: %.1f\n', ...
    pump1_pipe_idx, pump1_original_start_node, mat2str(pump1_connection_nodes), pump1_length_min, pump1_length_max, pump1_length_step);
fprintf('泵2管段号: %d, 原始起点: %d. 搜索接入节点: %s, 管长范围: [%.1f-%.1f], 步长: %.1f\n', ...
    pump2_pipe_idx, pump2_original_start_node, mat2str(pump2_connection_nodes), pump2_length_min, pump2_length_max, pump2_length_step);
disp(['水力计算最大迭代次数设置为: ', num2str(max_hydraulic_iterations)]);

total_combinations = length(pump1_connection_nodes) * length(pump2_connection_nodes) * ...
                     length(pump1_length_min:pump1_length_step:pump1_length_max) * ...
                     length(pump2_length_min:pump2_length_step:pump2_length_max);
disp(['预计总计算组合数: ', num2str(total_combinations)]);
combination_count = 0;

% --- 外层循环：改变泵1的接入点 ---
for p1_node_idx = 1:length(pump1_connection_nodes)
    current_p1_node = pump1_connection_nodes(p1_node_idx);

    % --- 第二层循环：改变泵2的接入点 ---
    for p2_node_idx = 1:length(pump2_connection_nodes)
        current_p2_node = pump2_connection_nodes(p2_node_idx);

        % --- 第三层循环：改变泵1的管长 ---
        for current_p1_L = pump1_length_min : pump1_length_step : pump1_length_max
            
            % --- 第四层循环：改变泵2的管长 ---
            for current_p2_L = pump2_length_min : pump2_length_step : pump2_length_max
                combination_count = combination_count + 1;
                fprintf('--- 测试组合 %d/%d: P1节点=%d, P1管长=%.1fm; P2节点=%d, P2管长=%.1fm ---\n', ...
                        combination_count, total_combinations, current_p1_node, current_p1_L, current_p2_node, current_p2_L);

                data2 = data2_orig; % 每次迭代都从原始数据开始，确保参数修改的独立性

                % --- 设置当前迭代的管网参数 ---
                % 泵1的参数设置
                data2(pump1_pipe_idx, 2) = pump1_original_start_node; % 泵1起点 (通常是水源节点)
                data2(pump1_pipe_idx, 3) = current_p1_node;           % 泵1终点 (接入管网的节点)
                data2(pump1_pipe_idx, 6) = current_p1_L;             % 泵1出水管管长
                
                % 泵2的参数设置
                data2(pump2_pipe_idx, 2) = pump2_original_start_node; % 泵2起点 (通常是水源节点)
                data2(pump2_pipe_idx, 3) = current_p2_node;           % 泵2终点 (接入管网的节点)
                data2(pump2_pipe_idx, 6) = current_p2_L;             % 泵2出水管管长

                % --- 基于 data2 更新相关参数 ---
                NM = data2(:,2:3); % 各管段的起点、终点节点编号矩阵 (N x 2)
                LD = data2(:,6);   % 各管段长度 (N x 1)
                D  = data2(:,7);   % 各管段管径 (N x 1)
                Q_initial  = data2(:,9); % 各管段初始流量 (N x 1)

                % --- 计算当前管段的流量系数 R (或阻力系数 S 的倒数相关值) ---
                % R_coeffs 代表 S = 1 / (R_coeffs^m_exp) 中的 R_coeffs，或者 hf = L*Q^m_exp / R_coeffs'
                % 此处采用海曾威廉公式 hf = L * Q^1.852 / (0.27853 * C * D^2.63)^1.852 / D^4.87
                % Q = (0.27853 * C * D^2.63 / L^0.54) * hf^0.54  (如果 m_exp=1/0.54 approx 1.852)
                % Q = R_coeff * hf ^ (1/m_exp)
                % R_coeff 在代码中是 Q/hf^(1/m_exp) 或 Q/hf^0.54
                R_coeffs = zeros(N,1);
                for i_R=1:N
                    if LD(i_R) <= 0 || D(i_R) <= 0 % 管长或管径无效
                        R_coeffs(i_R) = 0; % 视为断路或阻力无穷大，流量为0
                    else
                        % 根据海曾威廉公式 Q = K * C * D^2.63 * J^0.54, J = hf/L
                        % Q = (0.27853 * C_coeff * D(i_R)^2.63 / LD(i_R)^0.54) * (hf)^0.54
                        % 所以 R_coeffs(i_R) 就是 hf^0.54 前面的系数
                        R_coeffs(i_R) = (0.27853 * C_coeff * D(i_R)^2.63) / (LD(i_R)^0.54);
                    end
                end

                % 如果有泵站且LX~=0 (意味着要用水源数据中的虚耗系数)
                % 对于泵管，其流量计算可能基于泵的特性曲线或者给定的虚耗系数 SX
                % Q = (1/SX^0.5) * hf^0.5  (如果泵的特性简化为 h_pump - hf = SX * Q^2)
                % 这里假设SX是阻耗系数，S_pipe = SX, hf = S_pipe * Q^2
                % Q = (hf/SX)^0.5 = (1/SX^0.5) * hf^0.5
                % 所以这里的 R_coeffs 对于泵管来说是 1/SX^0.5
                if N_pump ~= 0 && LX ~= 0
                    for i_p_pump=1:N_pump % 遍历所有水源/泵
                         pump_pipe_actual_idx = NQ_pump_pipes(i_p_pump); % 获取当前泵在data2中的管段行号
                         if pump_pipe_actual_idx > 0 && pump_pipe_actual_idx <= N
                             if SX(i_p_pump) <= 0 % 虚阻耗系数无效
                                 R_coeffs(pump_pipe_actual_idx) = Inf; % 阻力为0，流量系数“无穷”（在计算Q时需特殊处理）
                             else
                                % m_exp 对于泵管通常认为是2，所以指数是0.5
                                R_coeffs(pump_pipe_actual_idx) = 1 / SX(i_p_pump)^0.5;
                             end
                         end
                    end
                end

                % --- 节点初始压头 ---
                E_calc_full = zeros(MT,1); % MT是总节点数（包括水源节点）
                E_calc_full(1:M) = 70;     % 普通节点初始水压设为70m (经验值)
                if N_source > 0 && LX ~=0 % 如果有水源且LX不为0
                    E_calc_full(M+1:MT) = HX(1:N_source); % 水源节点水压使用Excel中的HX值
                elseif N_source > 0 && LX == 0
                    disp('警告: 有水源但LX=0，水源压力HX未从Excel加载，可能导致计算不准确。');
                    % 也可以在此处为 E_calc_full(M+1:MT) 设置默认值或从其他地方获取
                end

                % --- 生成MIS,MJS矩阵 (节点-管段关联矩阵) ---
                % MIS(i,j): 与节点i相关联的第j根管线的编号，出流为正，入流为负
                % MJS(i,j): 与节点i相关联的第j根管线的另一端节点编号
                % MM_connected_pipes_count(i): 与节点i相关联的管线数目
                MIS = zeros(M, N); % 假设一个节点最多连接N条管（实际远小于N）
                MJS = zeros(M, N);
                MM_connected_pipes_count = zeros(M,1);
                for I_node=1:M % 遍历所有普通节点
                    K_conn_count=0; % 当前节点已连接的管段计数
                    for J_pipe=1:N % 遍历所有管段
                        if NM(J_pipe,1)==I_node % 如果管段起点是当前节点I_node
                            K_conn_count=K_conn_count+1;
                            MIS(I_node,K_conn_count)=J_pipe;      % 管段J_pipe从I_node流出
                            MJS(I_node,K_conn_count)=NM(J_pipe,2);% 管段J_pipe的另一端
                        end
                        if NM(J_pipe,2)==I_node % 如果管段终点是当前节点I_node
                            K_conn_count=K_conn_count+1;
                            MIS(I_node,K_conn_count)=-J_pipe;     % 管段J_pipe流入I_node
                            MJS(I_node,K_conn_count)=NM(J_pipe,1);% 管段J_pipe的另一端
                        end
                    end
                    MM_connected_pipes_count(I_node)=K_conn_count;
                end

                Q_calculated = Q_initial; % 使用初始流量开始迭代

                % --- 水力计算迭代 (KK循环，牛顿-拉夫逊法解非线性方程组) ---
                converged_hydraulics = false; % 水力计算收敛标志
                EQ_max_history = [];          % 记录每次迭代的最大流量不平衡量

                for KK=1:max_hydraulic_iterations
                    % (1) 根据当前节点水压 E_calc_full 计算各管段流量 Q_calculated
                    for I_p=1:N % 遍历所有管段
                        node1 = NM(I_p,1); % 管段起点节点编号
                        node2 = NM(I_p,2); % 管段终点节点编号
                        
                        % 确保节点编号在 E_calc_full 数组的有效范围内
                        if node1 > MT || node2 > MT || node1 < 1 || node2 < 1
                            error('管段 %d 连接的节点编号 (%d, %d) 超出总节点数 %d 的范围。请检查 data2 或 NM。', I_p, node1, node2, MT);
                        end
                        
                        ET_head_diff = E_calc_full(node1) - E_calc_full(node2); % 管段两端水头差

                        is_this_a_pump_pipe = false; % 标记当前管段是否为泵的出水管
                        current_pipe_m_exp = m_exp; % 默认为普通管段的摩阻指数 (1.852)
                        
                        if N_pump ~= 0 && LX ~= 0
                            for idx_pump_check = 1:N_pump % 遍历所有泵
                                if NQ_pump_pipes(idx_pump_check) == I_p % 如果当前管段I_p是某个泵的出水管
                                    is_this_a_pump_pipe = true;
                                    current_pipe_m_exp = 2; % 泵管的流量-水头损失关系通常按Q^2考虑
                                    break;
                                end
                            end
                        end

                        if R_coeffs(I_p) == 0 % 例如管长为0或管径为0，阻力无穷大或参数错误
                            Q_calculated(I_p) = 0;
                        elseif abs(ET_head_diff) < 1e-9 % 水头差极小，避免计算错误
                            Q_calculated(I_p) = 0;
                        elseif R_coeffs(I_p) == Inf % 例如虚耗系数为0的泵管，表示无阻力
                            % 这种情况比较特殊，理论上流量可以很大，取决于系统。
                            % 在实际模型中，应该有其他限制或泵的特性曲线。
                            % 为了数值稳定性，可以暂时设一个较大值或根据情况处理。
                            % 这里简单处理为基于一个微小阻力计算，或保持上一步流量。
                            % 或者，如果ET_head_diff是泵提供的扬程，则流量公式不同。
                            % 此处假设 R_coeffs(I_p) 是 Q = R_coeffs * hf^(1/m_exp) 中的系数
                            % 如果 R_coeffs = Inf, 意味着 hf 必须是0才能得到有限Q，除非 Q=0.
                             Q_calculated(I_p) = 1e6 * sign(ET_head_diff); % 示例：给一个大流量
                        else
                            if is_this_a_pump_pipe
                                % Q = R_coeff * hf^0.5, R_coeff = 1/SX^0.5
                                Q_calculated(I_p) = R_coeffs(I_p) * abs(ET_head_diff)^0.5 * sign(ET_head_diff);
                            else
                                % Q = R_coeff * hf^0.54, R_coeff from Hazen-Williams
                                Q_calculated(I_p) = R_coeffs(I_p) * abs(ET_head_diff)^(1/current_pipe_m_exp) * sign(ET_head_diff);
                            end
                        end
                    end

                    % (2) 计算各普通节点流量不平衡量 EQ (应为0)
                    EQ_imbalance = zeros(M,1); % 仅计算M个普通节点的流量平衡
                    if LX~=0 % 只有LX不为0时，才进行严格的流量平衡检查和牛顿迭代
                        for I_n=1:M % 遍历所有普通节点
                          EQ_imbalance(I_n)=QX(I_n); % 节点I_n的外部出流量 (出流为正，入流为负)
                          for J_conn=1:MM_connected_pipes_count(I_n) % 遍历连接到此节点的所有管段
                              pipe_abs_idx = abs(MIS(I_n,J_conn)); % 管段的绝对编号 (行号)
                              pipe_sign_in_MIS = sign(MIS(I_n,J_conn)); % 流向符号 (+1出, -1入节点I_n)
                              
                              % 注意：Q_calculated中的流量方向是基于node1 -> node2定义的
                              % MIS中的符号是基于流入/流出节点I_n定义的
                              % 如果管段j从节点i流出 (MIS(i,k)=j)，则贡献 -Q_calculated(j) 到节点i的平衡方程 (假设Q_calculated>0时是从NM(j,1)到NM(j,2))
                              % 如果管段j流入节点i (MIS(i,k)=-j)，则贡献 +Q_calculated(j) 到节点i的平衡方程
                              % 原代码中 EQ_imbalance(I_n) = EQ_imbalance(I_n) + Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS;
                              % 这个公式意味着：QX(节点出流量) + sum(连接管段流量 * MIS符号) = 0
                              % 如果MIS符号定义为出正入负，那么 QX - sum(Q_pipe_ออกจาก_i) + sum(Q_pipe_汇入_i) = 0
                              % 若Q_calculated(k)定义为从k1到k2为正：
                              %   若MIS(i,.) = k (k从i流出至i'), Q(k)为正，则节点i流出Q(k)，对节点方程贡献为 -Q(k)
                              %   若MIS(i,.) = -k (k从i'流入i), Q(k)为正(从k1到k2), 若k1=i',k2=i, 则实际流入，贡献为+Q(k)
                              %                           若k1=i, k2=i', 则实际流出, 贡献为-Q(k) -- 这里需要小心符号约定
                              % 根据许仕荣书 P66 (3-3-11), QX_i - sum(Q_ij) = 0, Q_ij为从i流向j的流量
                              % MIS(I_n, J_conn) > 0 表示管段 abs(MIS) 从 I_n 流出, 则 Q_ij = Q_calculated(pipe_abs_idx)
                              % MIS(I_n, J_conn) < 0 表示管段 abs(MIS) 流入 I_n, 则 Q_ji = Q_calculated(pipe_abs_idx) (假设Q_calc方向与管段定义一致)
                              % 因此, QX_i - sum_{j, MIS>0} Q_calculated(abs(MIS)) - sum_{k, MIS<0} (-Q_calculated(abs(MIS))) = 0
                              % QX_i - sum_{all connections L} Q_calculated(abs(MIS(L))) * sign(MIS(L)) = 0
                              % 所以, EQ_imbalance(I_n) = QX(I_n) - sum (Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS);
                              % 原代码是 QX + sum(Q*sign(MIS))， 我们需要的是 QX - sum(Q_out_effective), Q_out_effective = Q_calc * sign(MIS)
                              % 所以原代码 EQ_imbalance(I_n) = QX(I_n) + sum(Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS);
                              % 如果 QX 是外部需求（正为流出），则 QX - sum(Q_internal_net_outflow) = dQ
                              % Q_internal_net_outflow = sum_{pipe k connected to node I_n} Q_calculated(k) * flow_direction_factor(k, I_n)
                              % flow_direction_factor: +1 if Q_calculated(k) is away from I_n, -1 if towards I_n
                              % MIS(I_n, J_conn) positive means pipe J_conn starts at I_n. Q_calculated(J_conn) is away from I_n. Factor is +1.
                              % MIS(I_n, J_conn) negative means pipe J_conn ends at I_n. Q_calculated(J_conn) is towards I_n. Factor is -1.
                              % So, flow_direction_factor is sign(MIS(I_n, J_conn)).
                              % EQ_imbalance(I_n) = QX(I_n) - sum(Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS);
                              % 与原代码的符号相反，这取决于EQ的定义。原代码EQ是“不平衡量”，目标是使其为0。
                              % 如果是 A*x = b, 则 A*x - b 是不平衡量。这里是 f(H) = QX - sum(Q(H)) = 0
                              % 常见的牛顿法是 J * dH = -f(H) 或 J * dH = f(H) (取决于J的定义)
                              % 如果 J_ik = df_i / dH_k, 那么 dH = -J \ f(H)
                              % 如果 J_ik = -df_i / dH_k, 那么 dH = J \ f(H)
                              % 原代码的 AJ(i,i) = sum(dQ/dH_i) > 0, AJ(i,j) = -dQ/dH_j < 0 (for connected j)
                              % 这是对应于 f(H) = sum(Q(H)) - QX 的雅可比。
                              % 所以EQ_imbalance = sum(Q(H)) - QX.
                              % sum(Q(H))部分: Q_calculated(pipe_abs_idx)是基于 E(start)-E(end) 计算的。
                              % pipe_sign_in_MIS > 0: pipe starts at I_n. Q_calc is outflow. sum += Q_calc
                              % pipe_sign_in_MIS < 0: pipe ends at I_n. Q_calc is inflow. sum -= Q_calc (if Q_calc defined always positive based on its own start/end)
                              % Q_calculated本身带有方向，Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS 实际是净流出。
                              % 所以 EQ_imbalance(I_n) = sum_{pipes connected to I_n} (Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS) - QX(I_n)
                              % 原代码: EQ_imbalance(I_n) = QX(I_n) + sum(Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS);
                              % 这相当于 QX(I_n) - sum( Q_calculated_into_node ), 如果 Q_calculated 定义为流入节点为正。
                              % 让我们坚持原代码的定义，后续的雅可比矩阵和修正方向也是与之匹配的。
                              EQ_imbalance(I_n) = EQ_imbalance(I_n) + Q_calculated(pipe_abs_idx) * pipe_sign_in_MIS;
                          end
                        end
                    end

                    current_EQ_max = 0;
                    if LX~=0
                        current_EQ_max = max(abs(EQ_imbalance)); % 当前最大的节点流量不平衡绝对值
                        EQ_max_history = [EQ_max_history, current_EQ_max]; % 记录历史
                        if current_EQ_max < convergence_threshold_EQ && KK > 1 % 判断是否收敛
                            converged_hydraulics = true;
                            break; % 收敛则跳出KK迭代
                        end
                    elseif LX==0 && KK >= 10 % 如果LX=0，可能是不进行牛顿迭代的情况，迭代几次后默认完成
                         converged_hydraulics = true; % (或者设置一个不同的完成标准)
                         break;
                    end

                    % (3) 生成系数矩阵 AJ (雅可比矩阵的近似)
                    % QJ_derivatives(I_p) = dQ_p / d(ET_head_diff_p)
                    QJ_derivatives = zeros(N,1); % 存储每个管段流量对水头差的偏导数 dQ/dhf
                    for I_p=1:N
                        node1 = NM(I_p,1);
                        node2 = NM(I_p,2);
                        ET_head_diff = E_calc_full(node1) - E_calc_full(node2);
                        
                        % 为避免除零错误，当水头差接近零时，用一个微小值替代来计算导数
                        if abs(ET_head_diff) < 1e-7 
                            ET_for_deriv = 1e-7 * sign(ET_head_diff);
                            if ET_for_deriv == 0, ET_for_deriv = 1e-7; end % 确保不为0
                        else
                            ET_for_deriv = ET_head_diff;
                        end

                        is_this_a_pump_pipe_deriv = false;
                        current_pipe_m_exp_deriv = m_exp; % 默认1.852
                        if N_pump ~= 0 && LX ~= 0
                             for idx_pump_check_deriv = 1:N_pump
                                if NQ_pump_pipes(idx_pump_check_deriv) == I_p
                                    is_this_a_pump_pipe_deriv = true;
                                    current_pipe_m_exp_deriv = 2; % 泵管 Q prop hf^0.5
                                    break;
                                end
                             end
                        end
                        
                        if R_coeffs(I_p) == 0 || R_coeffs(I_p) == Inf || abs(ET_for_deriv) < 1e-9 % 无效R或水头差过小
                             QJ_derivatives(I_p) = 1e9; % 给一个很大的值，表示该管段水头变化对流量影响小，或者流量对水头变化敏感（如果dQ/dH很大）
                                                       % 实际上，如果 R_coeffs=0 (Q=0), dQ/dhf = 0.
                                                       % 如果 R_coeffs=Inf (hf=0 for finite Q), dQ/dhf = Inf.
                                                       % 原代码这里给1e9，可能是为了避免奇异矩阵，但物理意义需要斟酌
                                                       % 如果Q=R*hf^k, dQ/dhf = R*k*hf^(k-1)
                                                       % 如果R_coeffs(I_p) == 0, QJ_derivatives(I_p) 应该为 0.
                                                       % 如果R_coeffs(I_p) == Inf, 导数也趋于无穷.
                                                       % 我们按公式计算，如果R_coeff=0, 导数是0.
                             if R_coeffs(I_p) == 0
                                 QJ_derivatives(I_p) = 0;
                             else % R_coeffs is Inf or ET_for_deriv is tiny
                                 QJ_derivatives(I_p) = 1e9; % 保持原逻辑
                             end
                        else
                            if is_this_a_pump_pipe_deriv
                                % Q = R * hf^0.5 => dQ/dhf = R * 0.5 * hf^-0.5
                                QJ_derivatives(I_p) = R_coeffs(I_p) * 0.5 * abs(ET_for_deriv)^(-0.5);
                            else
                                % Q = R * hf^(1/m_exp) => dQ/dhf = R * (1/m_exp) * hf^((1/m_exp)-1)
                                exponent_val = 1/current_pipe_m_exp_deriv;
                                QJ_derivatives(I_p) = R_coeffs(I_p) * exponent_val * abs(ET_for_deriv)^(exponent_val - 1);
                            end
                        end
                        if isnan(QJ_derivatives(I_p)) || isinf(QJ_derivatives(I_p))
                            QJ_derivatives(I_p) = 1e9; % 防止计算出NaN或Inf
                        end
                    end

                    AJ_jacobian = zeros(M,M); % M个普通节点的雅可比矩阵
                    for I_n=1:M % 遍历雅可比矩阵的行 (对应节点I_n的流量平衡方程)
                      for J_conn=1:MM_connected_pipes_count(I_n) % 遍历连接到节点I_n的管段
                          pipe_abs_idx = abs(MIS(I_n,J_conn));     % 连接管段的编号
                          other_node_idx = MJS(I_n,J_conn);       % 连接管段的另一端节点
                          
                          % 对角元素 AJ(I_n,I_n) = sum (dQ_k / dH_In) for pipes k connected to I_n
                          AJ_jacobian(I_n,I_n) = AJ_jacobian(I_n,I_n) + QJ_derivatives(pipe_abs_idx);
                          
                          % 非对角元素 AJ(I_n, Other_node_idx) = - (dQ_k / dH_Other_node_idx) for pipe k between I_n and Other_node_idx
                          if other_node_idx <= M && other_node_idx >=1 % 只考虑普通节点之间的影响
                            AJ_jacobian(I_n,other_node_idx) = AJ_jacobian(I_n,other_node_idx) - QJ_derivatives(pipe_abs_idx);
                          end
                      end
                      if AJ_jacobian(I_n,I_n) == 0 % 防止对角线元素为0导致奇异矩阵
                          AJ_jacobian(I_n,I_n)=1e11; % 用一个非常大的数替代
                      end
                    end

                    % (4) 解线性方程组 AJ * DE_head_correction = EQ_imbalance，得到节点水压修正量 DE_head_correction
                    DE_head_correction = zeros(M,1);
                    if LX~=0 % 只有LX不为0时才解方程修正水压
                        if rcond(AJ_jacobian) < 1e-15 % 检查矩阵条件数，太小可能奇异
                            % disp(['  警告: 雅可比矩阵条件数过低 (', num2str(rcond(AJ_jacobian)), ')，可能奇异。跳过水压修正。']);
                            DE_head_correction = zeros(M,1); % 不修正或采用其他策略
                        else
                            DE_head_correction = AJ_jacobian \ EQ_imbalance; % 解线性方程组
                        end
                    end
                    
                    % 更新普通节点的水压 E_calc_full
                    for I_n=1:M
                        E_calc_full(I_n) = E_calc_full(I_n) - STR * DE_head_correction(I_n); % STR为松弛因子
                    end

                    if KK == max_hydraulic_iterations && ~converged_hydraulics && LX~=0
                         % 已在循环外统一提示
                    end
                end % KK循环结束 (水力计算迭代)

                if ~converged_hydraulics && LX~=0
                   fprintf('  注意: 当前组合水力计算未在 %d 次迭代内收束到阈值 %.1e。最后不平衡量: %.2e\n', ...
                           max_hydraulic_iterations, convergence_threshold_EQ, current_EQ_max);
                end

                % --- (3) 计算特定管段的压降和 (H_collect 和 calculated_sum_Hs) ---
                % 注意：这里的节点编号是 E_calc_full 中的绝对编号 (1 到 MT)
                % 需要确保这些节点编号是有效的
                H_collect.H1=E_calc_full(NM(1,1))-E_calc_full(NM(1,2)); % 假设H1是管段1的压降，以此类推
                H_collect.H2=E_calc_full(NM(2,1))-E_calc_full(NM(2,2));
                H_collect.H3=E_calc_full(NM(3,1))-E_calc_full(NM(3,2));
                H_collect.H4=E_calc_full(NM(4,1))-E_calc_full(NM(4,2));
                H_collect.H5=E_calc_full(NM(5,1))-E_calc_full(NM(5,2));
                H_collect.H6=E_calc_full(NM(6,1))-E_calc_full(NM(6,2));
                H_collect.H7=E_calc_full(NM(7,1))-E_calc_full(NM(7,2));
                H_collect.H8=E_calc_full(NM(8,1))-E_calc_full(NM(8,2));
                H_collect.H9=E_calc_full(NM(9,1))-E_calc_full(NM(9,2));
                H_collect.H10=E_calc_full(NM(10,1))-E_calc_full(NM(10,2));
                H_collect.H11=E_calc_full(NM(11,1))-E_calc_full(NM(11,2));
                H_collect.H12=E_calc_full(NM(12,1))-E_calc_full(NM(12,2));
                H_collect.H13=E_calc_full(NM(13,1))-E_calc_full(NM(13,2));
                H_collect.H14=E_calc_full(NM(14,1))-E_calc_full(NM(14,2));
                H_collect.H15=E_calc_full(NM(15,1))-E_calc_full(NM(15,2));
                H_collect.H16=E_calc_full(NM(16,1))-E_calc_full(NM(16,2));
                H_collect.H17=E_calc_full(NM(17,1))-E_calc_full(NM(17,2));
                H_collect.H18=E_calc_full(NM(18,1))-E_calc_full(NM(18,2));
                H_collect.H19=E_calc_full(NM(19,1))-E_calc_full(NM(19,2));
                H_collect.H20=E_calc_full(NM(20,1))-E_calc_full(NM(20,2));
                H_collect.H21=E_calc_full(NM(21,1))-E_calc_full(NM(21,2)); % 泵1管路水头损失 (假设管1为泵1出水管)
                H_collect.H22=E_calc_full(NM(22,1))-E_calc_full(NM(22,2)); % 泵2管路水头损失 (假设管5为泵2出水管)
                % 原代码 H_collect 使用的是固定的节点编号，例如 H_collect.H1=E_calc_full(13)-E_calc_full(1);
                % 这可能对应特定管段。如果管网结构会变，用NM(pipe_idx,:) 更通用。
                % 为了与原代码的sum_Hs定义保持一致，我们暂时保留原结构，但需要小心其代表的物理意义。
                % 如果NM矩阵的行顺序与H1..H22的管段定义一致，则上面的写法正确。
                % 否则，需要明确H1..H22具体指哪些管段或节点对。
                % 鉴于原代码的 H_collect 结构，我们假设这些特定节点对是固定的。
                % 例如，H1 是节点13和节点1之间的压差，但这可能不是管段1的压差。
                % 重新使用原代码的 H_collect 定义：
                if all([13,1,2,3,4,14,5,6,7,8,9,10,11,12,17,15,16] <= MT) % 确保节点号有效
                    H_collect_orig.H1=E_calc_full(13)-E_calc_full(1); H_collect_orig.H2=E_calc_full(1)-E_calc_full(2);
                    H_collect_orig.H3=E_calc_full(2)-E_calc_full(3); H_collect_orig.H4=E_calc_full(3)-E_calc_full(4);
                    H_collect_orig.H5=E_calc_full(14)-E_calc_full(4); H_collect_orig.H6=E_calc_full(1)-E_calc_full(5);
                    H_collect_orig.H7=E_calc_full(2)-E_calc_full(6); H_collect_orig.H8=E_calc_full(3)-E_calc_full(7);
                    H_collect_orig.H9=E_calc_full(4)-E_calc_full(8); H_collect_orig.H10=E_calc_full(5)-E_calc_full(6);
                    H_collect_orig.H11=E_calc_full(6)-E_calc_full(7); H_collect_orig.H12=E_calc_full(7)-E_calc_full(8);
                    H_collect_orig.H13=E_calc_full(5)-E_calc_full(9); H_collect_orig.H14=E_calc_full(6)-E_calc_full(10);
                    H_collect_orig.H15=E_calc_full(7)-E_calc_full(11); H_collect_orig.H16=E_calc_full(12)-E_calc_full(8); %注意节点顺序，可能是12-8或8-12
                    H_collect_orig.H17=E_calc_full(9)-E_calc_full(10); H_collect_orig.H18=E_calc_full(10)-E_calc_full(11);
                    H_collect_orig.H19=E_calc_full(11)-E_calc_full(12); H_collect_orig.H20=E_calc_full(17)-E_calc_full(12);
                    H_collect_orig.H21=E_calc_full(15)-E_calc_full(13); H_collect_orig.H22=E_calc_full(16)-E_calc_full(14);

                    calculated_sum_Hs = H_collect_orig.H1 + H_collect_orig.H5 + H_collect_orig.H2 + H_collect_orig.H3 + H_collect_orig.H4 + H_collect_orig.H10 + ...
                                        H_collect_orig.H6 + H_collect_orig.H7 + H_collect_orig.H8 + H_collect_orig.H9 + ...
                                        H_collect_orig.H11 + H_collect_orig.H12 + H_collect_orig.H13 + H_collect_orig.H14 + ...
                                        H_collect_orig.H15 + H_collect_orig.H16 + H_collect_orig.H17 + H_collect_orig.H18 + ...
                                        H_collect_orig.H19 + H_collect_orig.H20 + H_collect_orig.H21 + H_collect_orig.H22;
                else
                    calculated_sum_Hs = NaN; % 节点号无效，无法计算
                    disp('警告: H_collect_orig 计算中使用了无效的节点编号。');
                end
                
                fprintf('    当前组合 P1(%d,%.1fm),P2(%d,%.1fm): 计算得到的 sum_of_specified_Hs = %.4f\n', ...
                        current_p1_node, current_p1_L, current_p2_node, current_p2_L, calculated_sum_Hs);

                % --- 新增：更新所有测试组合中最低的总压降记录 ---
                if ~isnan(calculated_sum_Hs) && calculated_sum_Hs < min_overall_sum_Hs_solution.value
                    min_overall_sum_Hs_solution.value = calculated_sum_Hs;
                    min_overall_sum_Hs_solution.pump1_node = current_p1_node;
                    min_overall_sum_Hs_solution.pump2_node = current_p2_node;
                    min_overall_sum_Hs_solution.pump1_length = current_p1_L;
                    min_overall_sum_Hs_solution.pump2_length = current_p2_L;
                end
                % --- 新增结束 ---

                % --- 条件校验 ---
                % (1) 流量条件
                flow_ok = true;
                if isempty(Q_ref) || length(Q_calculated) ~= length(Q_ref)
                    % disp('  提示: 参考流量Q_ref为空或长度与计算流量不匹配，跳过流量校验。');
                    flow_ok = false; % 或者根据需求，若无参考则认为OK
                else
                    for i_q=1:N % 假设Q_ref和Q_calculated都是N维的
                        if abs(Q_ref(i_q)) < 1e-6 % 参考流量接近0
                            if abs(Q_calculated(i_q) - Q_ref(i_q)) > 1e-4 % 绝对误差
                                flow_ok = false; break;
                            end
                        else %相对误差
                            if abs(Q_calculated(i_q) - Q_ref(i_q)) / abs(Q_ref(i_q)) * 100 > flow_deviation_percent
                                flow_ok = false; break;
                            end
                        end
                    end
                end

                % (2) 节点压力条件
                pressure_ok = true;
                if isempty(E_ref) || length(E_calc_full) ~= length(E_ref)
                    % disp('  提示: 参考压力E_ref为空或长度与计算压力不匹配，跳过压力校验。');
                    pressure_ok = false; % 或者根据需求，若无参考则认为OK
                else
                    for i_e=1:MT % E_ref 和 E_calc_full 都是MT维的
                         if abs(E_ref(i_e)) < 1e-3 % 参考压力接近0
                            if abs(E_calc_full(i_e) - E_ref(i_e)) > 0.5 % 绝对误差(例如0.5m水头)
                                pressure_ok = false; break;
                            end
                        else % 相对误差
                            if abs(E_calc_full(i_e) - E_ref(i_e)) / abs(E_ref(i_e)) * 100 > head_deviation_percent
                                pressure_ok = false; break;
                            end
                        end
                    end
                end
                
                % (3) sum_Hs 条件
                sum_Hs_ok = false;
                if ~isnan(calculated_sum_Hs)
                    sum_Hs_ok = abs(calculated_sum_Hs - target_sum_Hs) <= sum_Hs_tolerance;
                end
                
                % --- 记录满足所有条件的解 ---
                % converged_hydraulics 对于 LX=0 的情况可能需要特殊处理其意义
                current_conditions_met = (converged_hydraulics || LX==0) && flow_ok && pressure_ok && sum_Hs_ok;

                if current_conditions_met
                    fprintf('  >>> 找到满足所有条件的解: P1节点=%d, P1管长=%.1fm; P2节点=%d, P2管长=%.1fm. sum_Hs = %.4f (目标 %.1f)\n', ...
                            current_p1_node, current_p1_L, current_p2_node, current_p2_L, calculated_sum_Hs, target_sum_Hs);

                    entry.pump1_node = current_p1_node;
                    entry.pump2_node = current_p2_node;
                    entry.pump1_length = current_p1_L;
                    entry.pump2_length = current_p2_L;
                    entry.sum_Hs = calculated_sum_Hs;
                    entry.Q_calculated = Q_calculated;
                    entry.E_calculated = E_calc_full;
                    entry.converged = converged_hydraulics;
                    if LX~=0, entry.max_EQ_imbalance = current_EQ_max; else, entry.max_EQ_imbalance = NaN; end
                    suitable_solutions = [suitable_solutions; entry];

                    current_sum_Hs_diff = abs(calculated_sum_Hs - target_sum_Hs);
                    if current_sum_Hs_diff < best_solution.metric_sum_Hs_diff
                        best_solution.metric_sum_Hs_diff = current_sum_Hs_diff;
                        best_solution.pump1_node = current_p1_node;
                        best_solution.pump2_node = current_p2_node;
                        best_solution.pump1_length = current_p1_L;
                        best_solution.pump2_length = current_p2_L;
                        best_solution.sum_Hs = calculated_sum_Hs;
                        best_solution.Q_calculated = Q_calculated;
                        best_solution.E_calculated = E_calc_full;
                        best_solution.converged = converged_hydraulics;
                        if LX~=0, best_solution.max_EQ_imbalance = current_EQ_max; else, best_solution.max_EQ_imbalance = NaN; end
                    end
                end
            end % current_p2_L 循环结束
        end % current_p1_L 循环结束
    end % p2_node_idx 循环结束
end % p1_node_idx 循环结束

% --- 显示最终结果 ---
disp('----------------------------------------------------------');
% --- 新增：显示搜索到的最低总压降 ---
if ~isinf(min_overall_sum_Hs_solution.value)
    fprintf('在所有测试的组合中，找到的最低管网总压降 (sum_of_specified_Hs) 是: %.4f\n', min_overall_sum_Hs_solution.value);
    fprintf('  该最低总压降对应的参数组合是:\n');
    fprintf('    泵1接入节点: %d, 泵1管长: %.1f m\n', min_overall_sum_Hs_solution.pump1_node, min_overall_sum_Hs_solution.pump1_length);
    fprintf('    泵2接入节点: %d, 泵2管长: %.1f m\n', min_overall_sum_Hs_solution.pump2_node, min_overall_sum_Hs_solution.pump2_length);
else
    disp('未能记录到任何组合的总压降数据 (可能循环未执行或calculated_sum_Hs始终为NaN/Inf)。');
end
disp('----------------------------------------------------------');
% --- 新增结束 ---

if ~isempty(suitable_solutions)
    fprintf('共找到 %d 个满足所有指定条件的方案。\n', size(suitable_solutions,1));
    if ~isnan(best_solution.pump1_node) % 检查best_solution是否被有效赋值
        disp('其中，sum_of_specified_Hs 最接近目标值 49 的方案是:');
        fprintf('  最佳泵1接入节点: %d\n', best_solution.pump1_node);
        fprintf('  最佳泵1管长: %.1f m\n', best_solution.pump1_length);
        fprintf('  最佳泵2接入节点: %d\n', best_solution.pump2_node);
        fprintf('  最佳泵2管长: %.1f m\n', best_solution.pump2_length);
        fprintf('  对应的 sum_of_specified_Hs: %.4f (与目标 %.1f 的差的绝对值为: %.4f)\n', ...
                best_solution.sum_Hs, target_sum_Hs, best_solution.metric_sum_Hs_diff);
        if LX ~=0
            fprintf('  水力计算收束状态: %s (最后最大节点流量不平衡量: %.2e)\n', mat2str(best_solution.converged), best_solution.max_EQ_imbalance);
        else
            fprintf('  水力计算完成状态 (LX=0): %s\n', mat2str(best_solution.converged)); % LX=0 时收敛性判断可能不同
        end

        disp('  该方案下的计算管段流量 Q_calculated:');
        disp(best_solution.Q_calculated'); % 转置后显示，更紧凑
        disp('  该方案下的计算节点压力 E_calculated (共 %d 个节点):');
        disp(best_solution.E_calculated'); % 转置后显示
    else
        disp('找到了满足条件的方案，但未能确定最佳方案（可能所有方案的sum_Hs差值相同或初始best_solution未被更优解覆盖）。');
    end
else
    disp('在指定的参数搜索范围内，未找到满足所有条件的方案。');
    disp('您可以尝试调整以下参数再进行搜索:');
    disp('  - 泵的接入节点列表');
    disp('  - 泵管的管长搜索范围 (min, max, step)');
    disp('  - 流量/压力的允许偏差百分比');
    disp(['  - sum_Hs 的目标值 (',num2str(target_sum_Hs), ') 或其允许误差 (', num2str(sum_Hs_tolerance),')']);
    disp(['  - 水力计算的迭代次数 (当前 ',num2str(max_hydraulic_iterations),') 或收敛阈值 (', num2str(convergence_threshold_EQ),')']);
end
disp('----------------------------------------------------------');

